// User and Authentication Types
export interface User {
  id: number;
  username: string;
  created_at: string;
}

export interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
}

// Project Types
export interface Project {
  id: number;
  name: string;
  description?: string;
  icon: string;
  color: string;
  created_at: string;
  updated_at: string;
}

export interface CreateProjectData {
  name: string;
  description?: string;
  icon?: string;
  color?: string;
}

// Page Types
export interface Page {
  id: number;
  project_id: number;
  parent_page_id?: number;
  title: string;
  content: string; // JSON string that can be parsed to ContentBlock[]
  icon: string;
  position: number;
  created_at: string;
  updated_at: string;
}

export interface CreatePageData {
  project_id: number;
  parent_page_id?: number;
  title: string;
  content?: string; // JSON string
  icon?: string;
  position?: number;
}

// Content Block Types
export type BlockType = 
  | 'paragraph'
  | 'heading1'
  | 'heading2'
  | 'heading3'
  | 'bulleted_list'
  | 'numbered_list'
  | 'todo'
  | 'quote'
  | 'divider'
  | 'file'
  | 'table'
  | 'code';

export interface ContentBlock {
  id: string;
  type: BlockType;
  content: any;
  position: number;
}

export interface TextBlock extends ContentBlock {
  type: 'paragraph' | 'heading1' | 'heading2' | 'heading3' | 'quote';
  content: {
    text: string;
    formatting?: TextFormatting[];
  };
}

export interface ListBlock extends ContentBlock {
  type: 'bulleted_list' | 'numbered_list';
  content: {
    items: ListItem[];
  };
}

export interface ListItem {
  id: string;
  text: string;
  formatting?: TextFormatting[];
  children?: ListItem[];
}

export interface TextFormatting {
  type: 'bold' | 'italic' | 'underline' | 'strikethrough' | 'code' | 'link';
  start: number;
  end: number;
  href?: string; // for links
}

// Todo Types
export interface Todo {
  id: number;
  page_id: number;
  title: string;
  description?: string;
  completed: boolean;
  priority: 'low' | 'medium' | 'high';
  due_date?: string;
  position: number;
  created_at: string;
  updated_at: string;
}

export interface CreateTodoData {
  page_id: number;
  title: string;
  description?: string;
  priority?: 'low' | 'medium' | 'high';
  due_date?: string;
  position?: number;
}

export interface TodoBlock extends ContentBlock {
  type: 'todo';
  content: {
    text: string;
    completed: boolean;
    formatting?: TextFormatting[];
  };
}

// File Types
export interface FileAttachment {
  id: number;
  project_id: number;
  page_id?: number;
  filename: string;
  original_name: string;
  file_path: string;
  file_size: number;
  mime_type: string;
  uploaded_at: string;
}

export interface FileBlock extends ContentBlock {
  type: 'file';
  content: {
    file_id: number;
    filename: string;
    file_size: number;
    mime_type: string;
  };
}

// Table Types
export interface CustomTable {
  id: number;
  page_id: number;
  name: string;
  schema: TableColumn[];
  created_at: string;
}

export interface TableColumn {
  id: string;
  name: string;
  type: 'text' | 'number' | 'date' | 'checkbox' | 'select';
  options?: string[]; // for select type
  required?: boolean;
}

export interface TableRow {
  id: number;
  table_id: number;
  data: Record<string, any>;
  position: number;
  created_at: string;
}

export interface TableBlock extends ContentBlock {
  type: 'table';
  content: {
    table_id: number;
    name: string;
  };
}

// UI State Types
export interface AppState {
  currentProject: Project | null;
  currentPage: Page | null;
  sidebarCollapsed: boolean;
  theme: 'light' | 'dark';
}

// API Response Types
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
}

// Search Types
export interface SearchResult {
  type: 'project' | 'page' | 'todo' | 'file';
  id: number;
  title: string;
  description?: string;
  project_name?: string;
  page_title?: string;
  highlight?: string;
}

// Navigation Types
export interface NavigationItem {
  id: string;
  label: string;
  icon: string;
  path: string;
  children?: NavigationItem[];
}

// Form Types
export interface LoginFormData {
  username: string;
  password: string;
}

export interface SetupFormData {
  username: string;
  password: string;
  confirmPassword: string;
}

// Component Props Types
export interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  children: React.ReactNode;
}

export interface ButtonProps {
  variant?: 'primary' | 'secondary' | 'ghost' | 'danger';
  size?: 'sm' | 'md' | 'lg';
  disabled?: boolean;
  loading?: boolean;
  children: React.ReactNode;
  onClick?: () => void;
  type?: 'button' | 'submit' | 'reset';
  className?: string;
}

export interface InputProps {
  label?: string;
  placeholder?: string;
  value: string;
  onChange: (value: string) => void;
  type?: 'text' | 'password' | 'email' | 'number' | 'date';
  required?: boolean;
  disabled?: boolean;
  error?: string;
  className?: string;
}
