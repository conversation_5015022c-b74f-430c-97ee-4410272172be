import React from 'react';
import { Sidebar } from './Sidebar';
import { Header } from './Header';

interface AppLayoutProps {
  children: React.ReactNode;
  sidebarCollapsed: boolean;
  onToggleSidebar: () => void;
  currentPath: string;
  projects: any[];
  onNavigate: (path: string) => void;
  onCreateProject: () => void;
  onLockApp: () => void;
  user: any;
  onLogout: () => void;
}

const AppLayout: React.FC<AppLayoutProps> = ({
  children,
  sidebarCollapsed,
  onToggleSidebar,
  currentPath,
  projects,
  onNavigate,
  onCreateProject,
  onLockApp,
  user,
  onLogout,
}) => {
  return (
    <div className="flex h-screen bg-gray-50">
      {/* Sidebar */}
      <Sidebar
        collapsed={sidebarCollapsed}
        onToggle={onToggleSidebar}
        currentPath={currentPath}
        projects={projects}
        onNavigate={onNavigate}
        onCreateProject={onCreateProject}
        onLockApp={onLockApp}
      />
      
      {/* Main Content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Header */}
        <Header
          user={user}
          onLogout={onLogout}
          currentPath={currentPath}
        />
        
        {/* Page Content */}
        <main className="flex-1 overflow-auto">
          {children}
        </main>
      </div>
    </div>
  );
};

export { AppLayout };
