import React, { useState } from 'react';
import { Search, User, LogOut, Settings } from 'lucide-react';
import { Button } from '../ui/Button';
import { Input } from '../ui/Input';

interface HeaderProps {
  user: any;
  onLogout: () => void;
  currentPath: string;
}

const Header: React.FC<HeaderProps> = ({ user, onLogout, currentPath }) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [showUserMenu, setShowUserMenu] = useState(false);

  const getPageTitle = (path: string) => {
    if (path === '/') return 'Dashboard';
    if (path === '/projects') return 'Projects';
    if (path === '/pages') return 'All Pages';
    if (path === '/todos') return 'All Todos';
    if (path === '/files') return 'Files';
    if (path === '/settings') return 'Settings';
    if (path.startsWith('/projects/')) return 'Project';
    return 'Flowist';
  };

  return (
    <header className="bg-white border-b border-gray-200 px-6 py-4">
      <div className="flex items-center justify-between">
        {/* Left side - Page title and search */}
        <div className="flex items-center gap-6">
          <h1 className="text-xl font-semibold text-gray-900">
            {getPageTitle(currentPath)}
          </h1>
          
          {/* Search */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Search projects, pages, todos..."
              value={searchQuery}
              onChange={(value) => setSearchQuery(value)}
              className="pl-10 w-80"
            />
          </div>
        </div>

        {/* Right side - User menu */}
        <div className="relative">
          <Button
            variant="ghost"
            onClick={() => setShowUserMenu(!showUserMenu)}
            className="flex items-center gap-2"
          >
            <User className="h-4 w-4" />
            <span className="hidden sm:inline">{user?.username}</span>
          </Button>

          {/* User dropdown menu */}
          {showUserMenu && (
            <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg border border-gray-200 py-1 z-50">
              <div className="px-4 py-2 border-b border-gray-100">
                <p className="text-sm font-medium text-gray-900">{user?.username}</p>
                <p className="text-xs text-gray-500">Signed in</p>
              </div>
              
              <button
                onClick={() => {
                  setShowUserMenu(false);
                  // Navigate to settings
                }}
                className="flex items-center gap-2 w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
              >
                <Settings className="h-4 w-4" />
                Settings
              </button>
              
              <button
                onClick={() => {
                  setShowUserMenu(false);
                  onLogout();
                }}
                className="flex items-center gap-2 w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
              >
                <LogOut className="h-4 w-4" />
                Sign out
              </button>
            </div>
          )}
        </div>
      </div>
    </header>
  );
};

export { Header };
