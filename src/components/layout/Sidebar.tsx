import React from 'react';
import { 
  Home, 
  FolderOpen, 
  FileText, 
  CheckSquare, 
  <PERSON>clip, 
  Settings, 
  Lock,
  Plus,
  ChevronRight,
  ChevronDown
} from 'lucide-react';
import { cn } from '../../utils/cn';
import { Button } from '../ui/Button';

interface SidebarItem {
  id: string;
  label: string;
  icon: React.ReactNode;
  path: string;
  active?: boolean;
  children?: SidebarItem[];
  expanded?: boolean;
}

interface SidebarProps {
  collapsed: boolean;
  onToggle: () => void;
  currentPath: string;
  projects: any[];
  onNavigate: (path: string) => void;
  onCreateProject: () => void;
  onLockApp: () => void;
}

const Sidebar: React.FC<SidebarProps> = ({
  collapsed,
  onToggle,
  currentPath,
  projects,
  onNavigate,
  onCreateProject,
  onLockApp,
}) => {
  const [expandedItems, setExpandedItems] = React.useState<Set<string>>(new Set(['projects']));

  const toggleExpanded = (itemId: string) => {
    const newExpanded = new Set(expandedItems);
    if (newExpanded.has(itemId)) {
      newExpanded.delete(itemId);
    } else {
      newExpanded.add(itemId);
    }
    setExpandedItems(newExpanded);
  };

  const mainItems: SidebarItem[] = [
    {
      id: 'dashboard',
      label: 'Dashboard',
      icon: <Home className="h-4 w-4" />,
      path: '/',
      active: currentPath === '/',
    },
    {
      id: 'projects',
      label: 'Projects',
      icon: <FolderOpen className="h-4 w-4" />,
      path: '/projects',
      active: currentPath.startsWith('/projects'),
      expanded: expandedItems.has('projects'),
      children: projects.map(project => ({
        id: `project-${project.id}`,
        label: project.name,
        icon: <span className="text-sm">{project.icon}</span>,
        path: `/projects/${project.id}`,
        active: currentPath === `/projects/${project.id}`,
      })),
    },
    {
      id: 'pages',
      label: 'All Pages',
      icon: <FileText className="h-4 w-4" />,
      path: '/pages',
      active: currentPath === '/pages',
    },
    {
      id: 'todos',
      label: 'All Todos',
      icon: <CheckSquare className="h-4 w-4" />,
      path: '/todos',
      active: currentPath === '/todos',
    },
    {
      id: 'files',
      label: 'Files',
      icon: <Paperclip className="h-4 w-4" />,
      path: '/files',
      active: currentPath === '/files',
    },
  ];

  const bottomItems: SidebarItem[] = [
    {
      id: 'settings',
      label: 'Settings',
      icon: <Settings className="h-4 w-4" />,
      path: '/settings',
      active: currentPath === '/settings',
    },
  ];

  const renderSidebarItem = (item: SidebarItem, level = 0) => {
    const hasChildren = item.children && item.children.length > 0;
    const isExpanded = expandedItems.has(item.id);

    return (
      <div key={item.id}>
        <div
          className={cn(
            'flex items-center gap-3 rounded-lg px-3 py-2 text-sm font-medium text-gray-700 transition-colors hover:bg-gray-100 hover:text-gray-900 cursor-pointer',
            item.active && 'bg-primary-50 text-primary-700 hover:bg-primary-100',
            level > 0 && 'ml-4 pl-6'
          )}
          onClick={() => {
            if (hasChildren) {
              toggleExpanded(item.id);
            } else {
              onNavigate(item.path);
            }
          }}
        >
          {hasChildren && (
            <button
              onClick={(e) => {
                e.stopPropagation();
                toggleExpanded(item.id);
              }}
              className="p-0.5 hover:bg-gray-200 rounded"
            >
              {isExpanded ? (
                <ChevronDown className="h-3 w-3" />
              ) : (
                <ChevronRight className="h-3 w-3" />
              )}
            </button>
          )}
          {!hasChildren && <div className="w-4" />}
          {item.icon}
          {!collapsed && <span className="flex-1">{item.label}</span>}
          {item.id === 'projects' && !collapsed && (
            <Button
              variant="ghost"
              size="sm"
              onClick={(e) => {
                e.stopPropagation();
                onCreateProject();
              }}
              className="p-1 h-auto opacity-0 group-hover:opacity-100"
            >
              <Plus className="h-3 w-3" />
            </Button>
          )}
        </div>
        
        {hasChildren && isExpanded && !collapsed && (
          <div className="mt-1">
            {item.children?.map(child => renderSidebarItem(child, level + 1))}
          </div>
        )}
      </div>
    );
  };

  return (
    <div
      className={cn(
        'flex flex-col h-full bg-white border-r border-gray-200 transition-all duration-300',
        collapsed ? 'w-16' : 'w-64'
      )}
    >
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200">
        {!collapsed && (
          <h1 className="text-lg font-semibold text-gray-900">Flowist</h1>
        )}
        <Button
          variant="ghost"
          size="sm"
          onClick={onToggle}
          className="p-2"
        >
          <div className="flex flex-col gap-1">
            <div className="w-4 h-0.5 bg-gray-600" />
            <div className="w-4 h-0.5 bg-gray-600" />
            <div className="w-4 h-0.5 bg-gray-600" />
          </div>
        </Button>
      </div>

      {/* Main Navigation */}
      <div className="flex-1 p-4 space-y-1 group">
        {mainItems.map(item => renderSidebarItem(item))}
      </div>

      {/* Bottom Navigation */}
      <div className="p-4 border-t border-gray-200 space-y-1">
        {bottomItems.map(item => renderSidebarItem(item))}
        <div
          className="flex items-center gap-3 rounded-lg px-3 py-2 text-sm font-medium text-gray-700 transition-colors hover:bg-gray-100 hover:text-gray-900 cursor-pointer"
          onClick={onLockApp}
        >
          <Lock className="h-4 w-4" />
          {!collapsed && <span>Lock App</span>}
        </div>
      </div>
    </div>
  );
};

export { Sidebar };
