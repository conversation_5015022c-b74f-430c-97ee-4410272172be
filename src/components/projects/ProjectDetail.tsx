import React, { useEffect, useState } from 'react';
import { Arrow<PERSON><PERSON><PERSON>, <PERSON>ting<PERSON>, MoreHorizontal } from 'lucide-react';
import { Button } from '../ui/Button';
import { PageEditor } from '../pages/PageEditor';
import { useProjectStore } from '../../stores/projectStore';


interface ProjectDetailProps {
  projectId: number;
  onBack: () => void;
}

const ProjectDetail: React.FC<ProjectDetailProps> = ({ projectId, onBack }) => {
  const [showSettings, setShowSettings] = useState(false);
  
  const {
    currentProject,
    isLoading,
    error,
    getProject,

  } = useProjectStore();

  useEffect(() => {
    getProject(projectId);
  }, [projectId, getProject]);

  if (isLoading && !currentProject) {
    return (
      <div className="h-full flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto mb-2"></div>
          <p className="text-gray-600">Loading project...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="h-full flex items-center justify-center">
        <div className="text-center">
          <p className="text-red-600 mb-4">{error}</p>
          <div className="space-x-2">
            <Button onClick={() => getProject(projectId)}>
              Try Again
            </Button>
            <Button variant="secondary" onClick={onBack}>
              Go Back
            </Button>
          </div>
        </div>
      </div>
    );
  }

  if (!currentProject) {
    return (
      <div className="h-full flex items-center justify-center">
        <div className="text-center">
          <p className="text-gray-600 mb-4">Project not found</p>
          <Button onClick={onBack}>Go Back</Button>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col">
      {/* Project Header */}
      <div className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={onBack}
              className="flex items-center gap-2"
            >
              <ArrowLeft className="h-4 w-4" />
              Back
            </Button>
            
            <div className="flex items-center gap-3">
              <div
                className="w-8 h-8 rounded-lg flex items-center justify-center text-white text-sm font-medium"
                style={{ backgroundColor: currentProject.color }}
              >
                {currentProject.icon}
              </div>
              <div>
                <h1 className="text-xl font-semibold text-gray-900">
                  {currentProject.name}
                </h1>
                {currentProject.description && (
                  <p className="text-sm text-gray-500">
                    {currentProject.description}
                  </p>
                )}
              </div>
            </div>
          </div>

          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowSettings(!showSettings)}
              className="flex items-center gap-2"
            >
              <Settings className="h-4 w-4" />
              Settings
            </Button>
            
            <Button
              variant="ghost"
              size="sm"
              className="p-2"
            >
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* Project Stats */}
        <div className="flex items-center gap-6 mt-4 text-sm text-gray-500">
          <span>Created {new Date(currentProject.created_at).toLocaleDateString()}</span>
          <span>•</span>
          <span>Last updated {new Date(currentProject.updated_at).toLocaleDateString()}</span>
          <span>•</span>
          <span>0 pages</span>
          <span>•</span>
          <span>0 todos</span>
        </div>
      </div>

      {/* Project Content */}
      <div className="flex-1 overflow-hidden">
        <PageEditor project={currentProject} />
      </div>

      {/* Settings Panel (if shown) */}
      {showSettings && (
        <div className="absolute top-16 right-6 w-80 bg-white rounded-lg shadow-lg border border-gray-200 p-4 z-10">
          <h3 className="font-semibold text-gray-900 mb-4">Project Settings</h3>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Project Name
              </label>
              <p className="text-sm text-gray-900">{currentProject.name}</p>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Description
              </label>
              <p className="text-sm text-gray-900">
                {currentProject.description || 'No description'}
              </p>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Icon & Color
              </label>
              <div className="flex items-center gap-2">
                <div
                  className="w-6 h-6 rounded flex items-center justify-center text-white text-xs"
                  style={{ backgroundColor: currentProject.color }}
                >
                  {currentProject.icon}
                </div>
                <span className="text-sm text-gray-600">{currentProject.color}</span>
              </div>
            </div>
            
            <div className="pt-2 border-t border-gray-200">
              <Button
                variant="secondary"
                size="sm"
                onClick={() => setShowSettings(false)}
                className="w-full"
              >
                Close
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export { ProjectDetail };
