import React, { useState, useEffect } from 'react';
import { Button } from '../ui/Button';
import { Input } from '../ui/Input';
import { Modal } from '../ui/Modal';
import { Project, CreateProjectData } from '../../types';

interface ProjectFormProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: CreateProjectData) => Promise<void>;
  project?: Project | null;
  isLoading?: boolean;
}

const PROJECT_ICONS = [
  '📁', '📊', '🚀', '💼', '🎯', '📱', '💻', '🎨', '📚', '🔧',
  '⚡', '🌟', '🎪', '🎭', '🎨', '🎵', '🎬', '📸', '🎮', '🏆',
  '💡', '🔬', '🧪', '🔍', '📈', '📉', '💰', '🏠', '🌍', '🚗'
];

const PROJECT_COLORS = [
  '#6366f1', '#8b5cf6', '#ec4899', '#ef4444', '#f97316',
  '#eab308', '#22c55e', '#10b981', '#06b6d4', '#3b82f6',
  '#6366f1', '#8b5cf6', '#ec4899', '#ef4444', '#f97316'
];

const ProjectForm: React.FC<ProjectFormProps> = ({
  isOpen,
  onClose,
  onSubmit,
  project,
  isLoading = false,
}) => {
  const [formData, setFormData] = useState<CreateProjectData>({
    name: '',
    description: '',
    icon: '📁',
    color: '#6366f1',
  });
  const [error, setError] = useState('');

  useEffect(() => {
    if (project) {
      setFormData({
        name: project.name,
        description: project.description || '',
        icon: project.icon,
        color: project.color,
      });
    } else {
      setFormData({
        name: '',
        description: '',
        icon: '📁',
        color: '#6366f1',
      });
    }
    setError('');
  }, [project, isOpen]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');

    if (!formData.name.trim()) {
      setError('Project name is required');
      return;
    }

    try {
      await onSubmit({
        ...formData,
        name: formData.name.trim(),
        description: formData.description?.trim() || undefined,
      });
      onClose();
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to save project');
    }
  };

  const handleInputChange = (field: keyof CreateProjectData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (error) setError('');
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={project ? 'Edit Project' : 'Create New Project'}
      description={project ? 'Update your project details' : 'Create a new project to organize your work'}
    >
      <form onSubmit={handleSubmit} className="space-y-6">
        {error && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md text-sm">
            {error}
          </div>
        )}

        <Input
          label="Project Name"
          value={formData.name}
          onChange={(value) => handleInputChange('name', value)}
          placeholder="Enter project name"
          required
          disabled={isLoading}
        />

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Description (optional)
          </label>
          <textarea
            value={formData.description}
            onChange={(e) => handleInputChange('description', e.target.value)}
            placeholder="Describe your project..."
            rows={3}
            disabled={isLoading}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 disabled:opacity-50"
          />
        </div>

        {/* Icon Selection */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Project Icon
          </label>
          <div className="grid grid-cols-10 gap-2">
            {PROJECT_ICONS.map((icon) => (
              <button
                key={icon}
                type="button"
                onClick={() => handleInputChange('icon', icon)}
                disabled={isLoading}
                className={`w-8 h-8 rounded-md flex items-center justify-center text-lg transition-colors ${
                  formData.icon === icon
                    ? 'bg-primary-100 border-2 border-primary-500'
                    : 'bg-gray-100 border border-gray-300 hover:bg-gray-200'
                }`}
              >
                {icon}
              </button>
            ))}
          </div>
        </div>

        {/* Color Selection */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Project Color
          </label>
          <div className="grid grid-cols-10 gap-2">
            {PROJECT_COLORS.map((color) => (
              <button
                key={color}
                type="button"
                onClick={() => handleInputChange('color', color)}
                disabled={isLoading}
                className={`w-8 h-8 rounded-md transition-all ${
                  formData.color === color
                    ? 'ring-2 ring-gray-400 ring-offset-2'
                    : 'hover:ring-2 hover:ring-gray-300 hover:ring-offset-1'
                }`}
                style={{ backgroundColor: color }}
              />
            ))}
          </div>
        </div>

        {/* Preview */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Preview
          </label>
          <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-md">
            <div
              className="w-10 h-10 rounded-lg flex items-center justify-center text-white text-lg"
              style={{ backgroundColor: formData.color }}
            >
              {formData.icon}
            </div>
            <div>
              <p className="font-medium text-gray-900">
                {formData.name || 'Project Name'}
              </p>
              {formData.description && (
                <p className="text-sm text-gray-500 truncate">
                  {formData.description}
                </p>
              )}
            </div>
          </div>
        </div>

        <div className="flex justify-end gap-3 pt-4">
          <Button
            type="button"
            variant="secondary"
            onClick={onClose}
            disabled={isLoading}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            loading={isLoading}
            disabled={isLoading}
          >
            {project ? 'Update Project' : 'Create Project'}
          </Button>
        </div>
      </form>
    </Modal>
  );
};

export { ProjectForm };
