import React, { useState } from 'react';
import { MoreH<PERSON><PERSON>tal, Edit, Trash2, FolderOpen } from 'lucide-react';
import { Card, CardContent } from '../ui/Card';
import { Button } from '../ui/Button';
import { Project } from '../../types';


interface ProjectCardProps {
  project: Project;
  onEdit: (project: Project) => void;
  onDelete: (project: Project) => void;
  onOpen: (project: Project) => void;
}

const ProjectCard: React.FC<ProjectCardProps> = ({
  project,
  onEdit,
  onDelete,
  onOpen,
}) => {
  const [showMenu, setShowMenu] = useState(false);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
    });
  };

  return (
    <Card className="group hover:shadow-md transition-shadow cursor-pointer relative">
      <CardContent className="p-6">
        {/* Project Header */}
        <div className="flex items-start justify-between mb-4">
          <div 
            className="flex items-center gap-3 flex-1 min-w-0"
            onClick={() => onOpen(project)}
          >
            <div
              className="w-10 h-10 rounded-lg flex items-center justify-center text-white text-lg font-medium"
              style={{ backgroundColor: project.color }}
            >
              {project.icon}
            </div>
            <div className="flex-1 min-w-0">
              <h3 className="font-semibold text-gray-900 truncate">
                {project.name}
              </h3>
              <p className="text-sm text-gray-500 mt-1">
                Updated {formatDate(project.updated_at)}
              </p>
            </div>
          </div>

          {/* Menu Button */}
          <div className="relative">
            <Button
              variant="ghost"
              size="sm"
              onClick={(e) => {
                e.stopPropagation();
                setShowMenu(!showMenu);
              }}
              className="opacity-0 group-hover:opacity-100 transition-opacity p-1"
            >
              <MoreHorizontal className="h-4 w-4" />
            </Button>

            {/* Dropdown Menu */}
            {showMenu && (
              <div className="absolute right-0 top-8 w-48 bg-white rounded-md shadow-lg border border-gray-200 py-1 z-10">
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    setShowMenu(false);
                    onOpen(project);
                  }}
                  className="flex items-center gap-2 w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                >
                  <FolderOpen className="h-4 w-4" />
                  Open Project
                </button>
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    setShowMenu(false);
                    onEdit(project);
                  }}
                  className="flex items-center gap-2 w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                >
                  <Edit className="h-4 w-4" />
                  Edit Project
                </button>
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    setShowMenu(false);
                    onDelete(project);
                  }}
                  className="flex items-center gap-2 w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50"
                >
                  <Trash2 className="h-4 w-4" />
                  Delete Project
                </button>
              </div>
            )}
          </div>
        </div>

        {/* Project Description */}
        {project.description && (
          <div onClick={() => onOpen(project)}>
            <p className="text-sm text-gray-600 line-clamp-2 mb-4">
              {project.description}
            </p>
          </div>
        )}

        {/* Project Stats */}
        <div 
          className="flex items-center justify-between text-xs text-gray-500"
          onClick={() => onOpen(project)}
        >
          <span>Created {formatDate(project.created_at)}</span>
          <div className="flex items-center gap-4">
            <span>0 pages</span>
            <span>0 todos</span>
          </div>
        </div>
      </CardContent>

      {/* Click overlay for mobile */}
      <div 
        className="absolute inset-0 md:hidden"
        onClick={() => onOpen(project)}
      />
    </Card>
  );
};

export { ProjectCard };
