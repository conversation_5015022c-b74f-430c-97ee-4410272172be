import React, { useState, useEffect } from 'react';
import { invoke } from '@tauri-apps/api/core';
import { LoginForm } from './LoginForm';
import { SetupForm } from './SetupForm';
import { useAuthStore } from '../../stores/authStore';

const AuthScreen: React.FC = () => {
  const [isSetupMode, setIsSetupMode] = useState(true); // Default to setup mode
  const [isCheckingSetup, setIsCheckingSetup] = useState(true);

  const { checkSetupRequired } = useAuthStore();

  useEffect(() => {
    const checkSetup = async () => {
      console.log('Starting authentication check...');

      try {
        // Test Tauri connection first
        const testResult = await invoke('test_connection');
        console.log('Tauri test result:', testResult);

        // Check if setup is required
        const setupRequired = await checkSetupRequired();
        console.log('Setup required:', setupRequired);

        setIsSetupMode(setupRequired);
      } catch (error) {
        console.error('Error during auth check:', error);
        // Default to setup mode for safety
        setIsSetupMode(true);
      }

      // Always stop loading after 2 seconds max
      setTimeout(() => {
        setIsCheckingSetup(false);
      }, 2000);
    };

    checkSetup();
  }, [checkSetupRequired]);

  if (isCheckingSetup) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading Flowist...</p>
          <button
            onClick={() => setIsCheckingSetup(false)}
            className="mt-4 px-4 py-2 bg-primary-600 text-white rounded hover:bg-primary-700"
          >
            Skip Loading
          </button>
        </div>
      </div>
    );
  }

  if (isSetupMode) {
    return (
      <SetupForm 
        onSwitchToLogin={() => setIsSetupMode(false)} 
      />
    );
  }

  return (
    <LoginForm 
      onSwitchToSetup={() => setIsSetupMode(true)} 
    />
  );
};

export { AuthScreen };
