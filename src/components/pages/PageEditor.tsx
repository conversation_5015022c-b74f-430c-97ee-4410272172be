import React, { useState, useEffect } from 'react';
import { Plus, FileText, Save } from 'lucide-react';
import { Button } from '../ui/Button';
import { Input } from '../ui/Input';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/Card';
import { RichTextEditor } from './RichTextEditor';
import { usePageStore } from '../../stores/pageStore';
import { Page, Project } from '../../types';

interface PageEditorProps {
  project: Project;
}

const PageEditor: React.FC<PageEditorProps> = ({ project }) => {
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [newPageTitle, setNewPageTitle] = useState('');
  const [pageContent, setPageContent] = useState('');
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);

  const {
    pages,
    currentPage,
    isLoading,
    error,
    fetchProjectPages,
    createPage,
    updatePage,
    setCurrentPage,
    clearError,
  } = usePageStore();

  useEffect(() => {
    fetchProjectPages(project.id);
  }, [project.id, fetchProjectPages]);

  const handleCreatePage = async () => {
    if (!newPageTitle.trim()) return;

    try {
      const newPage = await createPage({
        project_id: project.id,
        title: newPageTitle.trim(),
        content: '[]',
        icon: '📄',
      });
      
      setCurrentPage(newPage);
      setNewPageTitle('');
      setShowCreateForm(false);
    } catch (error) {
      console.error('Failed to create page:', error);
    }
  };

  const handleSelectPage = (page: Page) => {
    setCurrentPage(page);
  };

  return (
    <div className="flex h-full">
      {/* Page Tree Sidebar */}
      <div className="w-80 border-r border-gray-200 bg-white">
        <div className="p-4 border-b border-gray-200">
          <div className="flex items-center justify-between mb-4">
            <h2 className="font-semibold text-gray-900">Pages</h2>
            <Button
              size="sm"
              onClick={() => setShowCreateForm(true)}
              className="flex items-center gap-1"
            >
              <Plus className="h-3 w-3" />
              Add Page
            </Button>
          </div>

          {/* Create Page Form */}
          {showCreateForm && (
            <div className="space-y-3 p-3 bg-gray-50 rounded-md">
              <Input
                placeholder="Page title"
                value={newPageTitle}
                onChange={setNewPageTitle}
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    handleCreatePage();
                  } else if (e.key === 'Escape') {
                    setShowCreateForm(false);
                    setNewPageTitle('');
                  }
                }}
                autoFocus
              />
              <div className="flex gap-2">
                <Button
                  size="sm"
                  onClick={handleCreatePage}
                  disabled={!newPageTitle.trim() || isLoading}
                  loading={isLoading}
                >
                  Create
                </Button>
                <Button
                  size="sm"
                  variant="secondary"
                  onClick={() => {
                    setShowCreateForm(false);
                    setNewPageTitle('');
                  }}
                >
                  Cancel
                </Button>
              </div>
            </div>
          )}
        </div>

        {/* Error Message */}
        {error && (
          <div className="p-4 bg-red-50 border-b border-red-200">
            <p className="text-sm text-red-600">{error}</p>
            <button
              onClick={clearError}
              className="text-xs text-red-500 hover:text-red-700 underline mt-1"
            >
              Dismiss
            </button>
          </div>
        )}

        {/* Pages List */}
        <div className="p-2">
          {isLoading && pages.length === 0 ? (
            <div className="p-4 text-center text-gray-500">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary-600 mx-auto mb-2"></div>
              Loading pages...
            </div>
          ) : pages.length === 0 ? (
            <div className="p-4 text-center text-gray-500">
              <FileText className="h-8 w-8 mx-auto mb-2 text-gray-300" />
              <p className="text-sm">No pages yet</p>
              <p className="text-xs text-gray-400 mt-1">
                Create your first page to get started
              </p>
            </div>
          ) : (
            <div className="space-y-1">
              {pages.map((page) => (
                <button
                  key={page.id}
                  onClick={() => handleSelectPage(page)}
                  className={`w-full flex items-center gap-3 p-2 rounded-md text-left transition-colors ${
                    currentPage?.id === page.id
                      ? 'bg-primary-50 text-primary-700'
                      : 'hover:bg-gray-100 text-gray-700'
                  }`}
                >
                  <span className="text-sm">{page.icon}</span>
                  <span className="flex-1 text-sm font-medium truncate">
                    {page.title}
                  </span>
                </button>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Page Content */}
      <div className="flex-1 bg-gray-50">
        {currentPage ? (
          <div className="h-full">
            {/* Page Header */}
            <div className="bg-white border-b border-gray-200 p-6">
              <div className="flex items-center gap-3">
                <span className="text-2xl">{currentPage.icon}</span>
                <h1 className="text-2xl font-bold text-gray-900">
                  {currentPage.title}
                </h1>
              </div>
              <p className="text-sm text-gray-500 mt-2">
                Created {new Date(currentPage.created_at).toLocaleDateString()}
              </p>
            </div>

            {/* Page Content Area */}
            <div className="p-6">
              <Card>
                <CardHeader>
                  <CardTitle>Page Content</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="min-h-96 p-4 bg-gray-50 rounded-md">
                    <p className="text-gray-500 text-center">
                      Rich text editor coming soon...
                    </p>
                    <p className="text-xs text-gray-400 text-center mt-2">
                      This will be a Notion-like block-based editor
                    </p>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        ) : (
          <div className="h-full flex items-center justify-center">
            <div className="text-center">
              <FileText className="h-16 w-16 text-gray-300 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Select a page to edit
              </h3>
              <p className="text-gray-500 mb-6">
                Choose a page from the sidebar or create a new one to get started.
              </p>
              <Button
                onClick={() => setShowCreateForm(true)}
                className="flex items-center gap-2 mx-auto"
              >
                <Plus className="h-4 w-4" />
                Create New Page
              </Button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export { PageEditor };
