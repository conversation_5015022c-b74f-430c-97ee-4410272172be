import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { invoke } from '@tauri-apps/api/core';
import { User, AuthState } from '../types';

interface AuthStore extends AuthState {
  // Actions
  checkSetupRequired: () => Promise<boolean>;
  setupAccount: (username: string, password: string) => Promise<void>;
  login: (username: string, password: string) => Promise<void>;
  logout: () => void;
  getCurrentUser: (userId: number) => Promise<void>;
  updatePassword: (userId: number, oldPassword: string, newPassword: string) => Promise<void>;
  setLoading: (loading: boolean) => void;
}

interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
}

export const useAuthStore = create<AuthStore>()(
  persist(
    (set, get) => ({
      user: null,
      isAuthenticated: false,
      isLoading: false,

      setLoading: (loading: boolean) => {
        set({ isLoading: loading });
      },

      checkSetupRequired: async (): Promise<boolean> => {
        try {
          console.log('AuthStore: Starting setup check...');
          set({ isLoading: true });
          const response: ApiResponse<boolean> = await invoke('check_setup_required');
          console.log('AuthStore: Setup check response:', response);

          if (response.success && response.data !== undefined) {
            return response.data;
          } else {
            throw new Error(response.error || 'Failed to check setup status');
          }
        } catch (error) {
          console.error('AuthStore: Error checking setup status:', error);
          throw error;
        } finally {
          set({ isLoading: false });
        }
      },

      setupAccount: async (username: string, password: string): Promise<void> => {
        try {
          set({ isLoading: true });
          const response: ApiResponse<User> = await invoke('setup_account', {
            request: { username, password }
          });
          
          if (response.success && response.data) {
            set({
              user: response.data,
              isAuthenticated: true,
              isLoading: false,
            });
          } else {
            throw new Error(response.error || 'Failed to setup account');
          }
        } catch (error) {
          set({ isLoading: false });
          throw error;
        }
      },

      login: async (username: string, password: string): Promise<void> => {
        try {
          set({ isLoading: true });
          const response: ApiResponse<User> = await invoke('login', {
            request: { username, password }
          });
          
          if (response.success && response.data) {
            set({
              user: response.data,
              isAuthenticated: true,
              isLoading: false,
            });
          } else {
            throw new Error(response.error || 'Invalid credentials');
          }
        } catch (error) {
          set({ isLoading: false });
          throw error;
        }
      },

      logout: () => {
        set({
          user: null,
          isAuthenticated: false,
          isLoading: false,
        });
      },

      getCurrentUser: async (userId: number): Promise<void> => {
        try {
          set({ isLoading: true });
          const response: ApiResponse<User> = await invoke('get_current_user', { userId });
          
          if (response.success && response.data) {
            set({
              user: response.data,
              isAuthenticated: true,
              isLoading: false,
            });
          } else {
            // If we can't get the current user, logout
            get().logout();
          }
        } catch (error) {
          console.error('Error getting current user:', error);
          get().logout();
        }
      },

      updatePassword: async (userId: number, oldPassword: string, newPassword: string): Promise<void> => {
        try {
          set({ isLoading: true });
          const response: ApiResponse<void> = await invoke('update_password', {
            userId,
            oldPassword,
            newPassword,
          });
          
          if (!response.success) {
            throw new Error(response.error || 'Failed to update password');
          }
        } catch (error) {
          throw error;
        } finally {
          set({ isLoading: false });
        }
      },
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({
        user: state.user,
        isAuthenticated: state.isAuthenticated,
      }),
    }
  )
);
