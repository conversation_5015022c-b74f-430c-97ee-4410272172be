import { create } from 'zustand';
import { invoke } from '@tauri-apps/api/core';
import { Project, CreateProjectData } from '../types';

interface ProjectStore {
  projects: Project[];
  currentProject: Project | null;
  isLoading: boolean;
  error: string | null;

  // Actions
  fetchProjects: () => Promise<void>;
  createProject: (data: CreateProjectData) => Promise<Project>;
  updateProject: (id: number, data: Partial<CreateProjectData>) => Promise<Project>;
  deleteProject: (id: number) => Promise<void>;
  getProject: (id: number) => Promise<Project>;
  setCurrentProject: (project: Project | null) => void;
  searchProjects: (query: string) => Promise<Project[]>;
  clearError: () => void;
}

interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
}

export const useProjectStore = create<ProjectStore>((set) => ({
  projects: [],
  currentProject: null,
  isLoading: false,
  error: null,

  clearError: () => set({ error: null }),

  setCurrentProject: (project: Project | null) => {
    set({ currentProject: project });
  },

  fetchProjects: async () => {
    try {
      set({ isLoading: true, error: null });
      
      const response: ApiResponse<Project[]> = await invoke('get_projects');
      
      if (response.success && response.data) {
        set({ projects: response.data });
      } else {
        throw new Error(response.error || 'Failed to fetch projects');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to fetch projects';
      set({ error: errorMessage });
      console.error('Error fetching projects:', error);
    } finally {
      set({ isLoading: false });
    }
  },

  createProject: async (data: CreateProjectData): Promise<Project> => {
    try {
      set({ isLoading: true, error: null });
      
      const response: ApiResponse<Project> = await invoke('create_new_project', {
        request: data
      });
      
      if (response.success && response.data) {
        const newProject = response.data;
        set(state => ({
          projects: [newProject, ...state.projects]
        }));
        return newProject;
      } else {
        throw new Error(response.error || 'Failed to create project');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to create project';
      set({ error: errorMessage });
      throw error;
    } finally {
      set({ isLoading: false });
    }
  },

  updateProject: async (id: number, data: Partial<CreateProjectData>): Promise<Project> => {
    try {
      set({ isLoading: true, error: null });
      
      const response: ApiResponse<Project> = await invoke('update_project_details', {
        projectId: id,
        request: data
      });
      
      if (response.success && response.data) {
        const updatedProject = response.data;
        set(state => ({
          projects: state.projects.map(p => p.id === id ? updatedProject : p),
          currentProject: state.currentProject?.id === id ? updatedProject : state.currentProject
        }));
        return updatedProject;
      } else {
        throw new Error(response.error || 'Failed to update project');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to update project';
      set({ error: errorMessage });
      throw error;
    } finally {
      set({ isLoading: false });
    }
  },

  deleteProject: async (id: number): Promise<void> => {
    try {
      set({ isLoading: true, error: null });
      
      const response: ApiResponse<void> = await invoke('delete_project_by_id', {
        projectId: id
      });
      
      if (response.success) {
        set(state => ({
          projects: state.projects.filter(p => p.id !== id),
          currentProject: state.currentProject?.id === id ? null : state.currentProject
        }));
      } else {
        throw new Error(response.error || 'Failed to delete project');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to delete project';
      set({ error: errorMessage });
      throw error;
    } finally {
      set({ isLoading: false });
    }
  },

  getProject: async (id: number): Promise<Project> => {
    try {
      set({ isLoading: true, error: null });
      
      const response: ApiResponse<Project> = await invoke('get_project', {
        projectId: id
      });
      
      if (response.success && response.data) {
        const project = response.data;
        set({ currentProject: project });
        return project;
      } else {
        throw new Error(response.error || 'Failed to get project');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to get project';
      set({ error: errorMessage });
      throw error;
    } finally {
      set({ isLoading: false });
    }
  },

  searchProjects: async (query: string): Promise<Project[]> => {
    try {
      set({ isLoading: true, error: null });
      
      const response: ApiResponse<Project[]> = await invoke('search_projects_by_query', {
        query
      });
      
      if (response.success && response.data) {
        return response.data;
      } else {
        throw new Error(response.error || 'Failed to search projects');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to search projects';
      set({ error: errorMessage });
      throw error;
    } finally {
      set({ isLoading: false });
    }
  },
}));
