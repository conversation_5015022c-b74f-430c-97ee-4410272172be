import { create } from 'zustand';
import { invoke } from '@tauri-apps/api/core';
import { Page, CreatePageData } from '../types';

interface PageStore {
  pages: Page[];
  currentPage: Page | null;
  isLoading: boolean;
  error: string | null;

  // Actions
  fetchProjectPages: (projectId: number) => Promise<void>;
  createPage: (data: CreatePageData) => Promise<Page>;
  getPage: (id: number) => Promise<Page>;
  setCurrentPage: (page: Page | null) => void;
  clearError: () => void;
}

interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
}

export const usePageStore = create<PageStore>((set) => ({
  pages: [],
  currentPage: null,
  isLoading: false,
  error: null,

  clearError: () => set({ error: null }),

  setCurrentPage: (page: Page | null) => {
    set({ currentPage: page });
  },

  fetchProjectPages: async (projectId: number) => {
    try {
      set({ isLoading: true, error: null });
      
      const response: ApiResponse<Page[]> = await invoke('get_project_pages', {
        projectId
      });
      
      if (response.success && response.data) {
        set({ pages: response.data });
      } else {
        throw new Error(response.error || 'Failed to fetch pages');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to fetch pages';
      set({ error: errorMessage });
      console.error('Error fetching pages:', error);
    } finally {
      set({ isLoading: false });
    }
  },

  createPage: async (data: CreatePageData): Promise<Page> => {
    try {
      set({ isLoading: true, error: null });
      
      const response: ApiResponse<Page> = await invoke('create_new_page', {
        request: {
          ...data,
          content: data.content ? JSON.stringify(data.content) : '[]'
        }
      });
      
      if (response.success && response.data) {
        const newPage = response.data;
        set(state => ({
          pages: [...state.pages, newPage]
        }));
        return newPage;
      } else {
        throw new Error(response.error || 'Failed to create page');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to create page';
      set({ error: errorMessage });
      throw error;
    } finally {
      set({ isLoading: false });
    }
  },

  getPage: async (id: number): Promise<Page> => {
    try {
      set({ isLoading: true, error: null });
      
      const response: ApiResponse<Page> = await invoke('get_page', {
        pageId: id
      });
      
      if (response.success && response.data) {
        const page = response.data;
        set({ currentPage: page });
        return page;
      } else {
        throw new Error(response.error || 'Failed to get page');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to get page';
      set({ error: errorMessage });
      throw error;
    } finally {
      set({ isLoading: false });
    }
  },
}));
