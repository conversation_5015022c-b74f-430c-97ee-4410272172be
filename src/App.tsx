import { useState, useEffect } from 'react';
import { AppLayout } from './components/layout/AppLayout';
import { Dashboard } from './components/dashboard/Dashboard';
import { ProjectDetail } from './components/projects/ProjectDetail';
import { TodoList } from './components/todos/TodoList';
import { AuthScreen } from './components/auth/AuthScreen';
import { useProjectStore } from './stores/projectStore';
import { useAuthStore } from './stores/authStore';
import { Project } from './types';
import "./App.css";

function App() {
  const [currentPath, setCurrentPath] = useState('/');
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);

  const { projects, fetchProjects } = useProjectStore();
  const { user, isAuthenticated, logout } = useAuthStore();

  useEffect(() => {
    // Load projects only if authenticated
    if (isAuthenticated && user) {
      fetchProjects();
    }
  }, [isAuthenticated, user, fetchProjects]);

  const handleNavigate = (path: string) => {
    setCurrentPath(path);
  };

  const handleNavigateToProject = (project: Project) => {
    setCurrentPath(`/projects/${project.id}`);
  };

  const handleCreateProject = () => {
    // This will be handled by the Dashboard component
    console.log('Create project triggered from sidebar');
  };

  const handleLockApp = () => {
    logout();
  };

  const handleLogout = () => {
    logout();
  };

  // Show authentication screen if not authenticated
  if (!isAuthenticated || !user) {
    return <AuthScreen />;
  }

  const renderCurrentPage = () => {
    switch (currentPath) {
      case '/':
        return <Dashboard onNavigateToProject={handleNavigateToProject} />;
      case '/projects':
        return <Dashboard onNavigateToProject={handleNavigateToProject} />;
      case '/pages':
        return (
          <div className="p-6">
            <h1 className="text-2xl font-bold text-gray-900">All Pages</h1>
            <p className="text-gray-600 mt-2">Coming soon...</p>
          </div>
        );
      case '/todos':
        return <TodoList />;
      case '/files':
        return (
          <div className="p-6">
            <h1 className="text-2xl font-bold text-gray-900">Files</h1>
            <p className="text-gray-600 mt-2">Coming soon...</p>
          </div>
        );
      case '/settings':
        return (
          <div className="p-6">
            <h1 className="text-2xl font-bold text-gray-900">Settings</h1>
            <p className="text-gray-600 mt-2">Coming soon...</p>
          </div>
        );
      default:
        if (currentPath.startsWith('/projects/')) {
          const projectId = parseInt(currentPath.split('/')[2]);
          if (projectId) {
            return (
              <ProjectDetail
                projectId={projectId}
                onBack={() => setCurrentPath('/')}
              />
            );
          }
        }
        return <Dashboard onNavigateToProject={handleNavigateToProject} />;
    }
  };

  return (
    <AppLayout
      sidebarCollapsed={sidebarCollapsed}
      onToggleSidebar={() => setSidebarCollapsed(!sidebarCollapsed)}
      currentPath={currentPath}
      projects={projects}
      onNavigate={handleNavigate}
      onCreateProject={handleCreateProject}
      onLockApp={handleLockApp}
      user={user}
      onLogout={handleLogout}
    >
      {renderCurrentPage()}
    </AppLayout>
  );
}

export default App;
