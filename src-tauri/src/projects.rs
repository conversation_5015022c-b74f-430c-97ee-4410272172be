use rusqlite::params;
use serde::{Deserialize, Serialize};
use tauri::AppHandle;

use crate::database::{get_connection, Project};

#[derive(Debug, Serialize, Deserialize)]
pub struct CreateProjectRequest {
    pub name: String,
    pub description: Option<String>,
    pub icon: Option<String>,
    pub color: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct UpdateProjectRequest {
    pub name: Option<String>,
    pub description: Option<String>,
    pub icon: Option<String>,
    pub color: Option<String>,
}

pub fn create_project(
    app_handle: &AppHandle,
    request: CreateProjectRequest,
) -> Result<Project, Box<dyn std::error::Error>> {
    let conn = get_connection(app_handle)?;
    
    let icon = request.icon.unwrap_or_else(|| "📁".to_string());
    let color = request.color.unwrap_or_else(|| "#6366f1".to_string());
    
    conn.execute(
        "INSERT INTO projects (name, description, icon, color) VALUES (?1, ?2, ?3, ?4)",
        params![request.name, request.description, icon, color],
    )?;
    
    let project_id = conn.last_insert_rowid();
    get_project_by_id(app_handle, project_id)
}

pub fn get_project_by_id(
    app_handle: &AppHandle,
    project_id: i64,
) -> Result<Project, Box<dyn std::error::Error>> {
    let conn = get_connection(app_handle)?;
    
    let mut stmt = conn.prepare(
        "SELECT id, name, description, icon, color, created_at, updated_at 
         FROM projects WHERE id = ?1"
    )?;
    
    let project = stmt.query_row(params![project_id], |row| {
        Ok(Project {
            id: row.get(0)?,
            name: row.get(1)?,
            description: row.get(2)?,
            icon: row.get(3)?,
            color: row.get(4)?,
            created_at: row.get(5)?,
            updated_at: row.get(6)?,
        })
    })?;
    
    Ok(project)
}

pub fn get_all_projects(app_handle: &AppHandle) -> Result<Vec<Project>, Box<dyn std::error::Error>> {
    let conn = get_connection(app_handle)?;
    
    let mut stmt = conn.prepare(
        "SELECT id, name, description, icon, color, created_at, updated_at 
         FROM projects ORDER BY updated_at DESC"
    )?;
    
    let project_iter = stmt.query_map([], |row| {
        Ok(Project {
            id: row.get(0)?,
            name: row.get(1)?,
            description: row.get(2)?,
            icon: row.get(3)?,
            color: row.get(4)?,
            created_at: row.get(5)?,
            updated_at: row.get(6)?,
        })
    })?;
    
    let mut projects = Vec::new();
    for project in project_iter {
        projects.push(project?);
    }
    
    Ok(projects)
}

pub fn update_project(
    app_handle: &AppHandle,
    project_id: i64,
    request: UpdateProjectRequest,
) -> Result<Project, Box<dyn std::error::Error>> {
    let conn = get_connection(app_handle)?;
    
    // Build dynamic update query
    let mut updates = Vec::new();
    let mut params_vec = Vec::new();
    
    if let Some(name) = &request.name {
        updates.push("name = ?");
        params_vec.push(name.as_str());
    }
    
    if let Some(description) = &request.description {
        updates.push("description = ?");
        params_vec.push(description.as_str());
    }
    
    if let Some(icon) = &request.icon {
        updates.push("icon = ?");
        params_vec.push(icon.as_str());
    }
    
    if let Some(color) = &request.color {
        updates.push("color = ?");
        params_vec.push(color.as_str());
    }
    
    if updates.is_empty() {
        return get_project_by_id(app_handle, project_id);
    }
    
    updates.push("updated_at = CURRENT_TIMESTAMP");
    let project_id_str = project_id.to_string();
    params_vec.push(&project_id_str);

    let query = format!(
        "UPDATE projects SET {} WHERE id = ?",
        updates.join(", ")
    );

    conn.execute(&query, rusqlite::params_from_iter(params_vec))?;
    
    get_project_by_id(app_handle, project_id)
}

pub fn delete_project(
    app_handle: &AppHandle,
    project_id: i64,
) -> Result<(), Box<dyn std::error::Error>> {
    let conn = get_connection(app_handle)?;
    
    // Delete in order due to foreign key constraints
    // First delete table_rows
    conn.execute(
        "DELETE FROM table_rows WHERE table_id IN (
            SELECT id FROM custom_tables WHERE page_id IN (
                SELECT id FROM pages WHERE project_id = ?1
            )
        )",
        params![project_id],
    )?;
    
    // Delete custom_tables
    conn.execute(
        "DELETE FROM custom_tables WHERE page_id IN (
            SELECT id FROM pages WHERE project_id = ?1
        )",
        params![project_id],
    )?;
    
    // Delete todos
    conn.execute(
        "DELETE FROM todos WHERE page_id IN (
            SELECT id FROM pages WHERE project_id = ?1
        )",
        params![project_id],
    )?;
    
    // Delete files
    conn.execute(
        "DELETE FROM files WHERE project_id = ?1",
        params![project_id],
    )?;
    
    // Delete pages
    conn.execute(
        "DELETE FROM pages WHERE project_id = ?1",
        params![project_id],
    )?;
    
    // Finally delete the project
    conn.execute(
        "DELETE FROM projects WHERE id = ?1",
        params![project_id],
    )?;
    
    Ok(())
}

pub fn search_projects(
    app_handle: &AppHandle,
    query: &str,
) -> Result<Vec<Project>, Box<dyn std::error::Error>> {
    let conn = get_connection(app_handle)?;
    
    let search_pattern = format!("%{}%", query);
    
    let mut stmt = conn.prepare(
        "SELECT id, name, description, icon, color, created_at, updated_at 
         FROM projects 
         WHERE name LIKE ?1 OR description LIKE ?1
         ORDER BY updated_at DESC"
    )?;
    
    let project_iter = stmt.query_map(params![search_pattern], |row| {
        Ok(Project {
            id: row.get(0)?,
            name: row.get(1)?,
            description: row.get(2)?,
            icon: row.get(3)?,
            color: row.get(4)?,
            created_at: row.get(5)?,
            updated_at: row.get(6)?,
        })
    })?;
    
    let mut projects = Vec::new();
    for project in project_iter {
        projects.push(project?);
    }
    
    Ok(projects)
}
