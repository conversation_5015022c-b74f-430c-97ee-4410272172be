mod database;
mod auth;
mod projects;
mod pages;
mod todos;
mod files;
mod tables;
mod commands;



#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
    tauri::Builder::default()
        .plugin(tauri_plugin_opener::init())
        .plugin(tauri_plugin_fs::init())
        .plugin(tauri_plugin_dialog::init())
        .setup(|app| {
            // Initialize database
            if let Err(e) = database::init_database(app.handle()) {
                eprintln!("Failed to initialize database: {}", e);
                std::process::exit(1);
            }
            Ok(())
        })
        .invoke_handler(tauri::generate_handler![
            commands::test_connection,
            // Auth commands
            commands::check_setup_required,
            commands::setup_account,
            commands::login,
            commands::get_current_user,
            commands::update_password,
            // Project commands
            commands::create_new_project,
            commands::get_project,
            commands::get_projects,
            commands::update_project_details,
            commands::delete_project_by_id,
            commands::search_projects_by_query,
            // Page commands
            commands::create_new_page,
            commands::get_page,
            commands::get_project_pages,
            commands::update_page_details,
            commands::delete_page_by_id,
            commands::search_pages_by_query,
            // Todo commands
            commands::create_new_todo,
            commands::get_todo,
            commands::get_page_todos,
            commands::get_todos,
            commands::update_todo_details,
            commands::delete_todo_by_id,
            commands::search_todos_by_query,
            // File commands
            commands::upload_file,
            commands::get_file,
            commands::get_project_files,
            commands::get_page_files,
            commands::get_files,
            commands::update_file_details,
            commands::delete_file_by_id,
            commands::download_file,
            commands::search_files_by_query,
            // Table commands
            commands::create_new_table,
            commands::get_table,
            commands::get_page_tables,
            commands::update_table_details,
            commands::delete_table_by_id,
            // Table row commands
            commands::create_table_row,
            commands::get_table_row,
            commands::get_table_rows,
            commands::update_table_row,
            commands::delete_table_row,
        ])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
