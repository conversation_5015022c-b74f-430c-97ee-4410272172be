mod database;
mod auth;
mod projects;
mod pages;
mod todos;
mod commands;



#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
    tauri::Builder::default()
        .plugin(tauri_plugin_opener::init())
        .plugin(tauri_plugin_fs::init())
        .plugin(tauri_plugin_dialog::init())
        .setup(|app| {
            // Initialize database
            if let Err(e) = database::init_database(app.handle()) {
                eprintln!("Failed to initialize database: {}", e);
                std::process::exit(1);
            }
            Ok(())
        })
        .invoke_handler(tauri::generate_handler![
            commands::test_connection,
            commands::check_setup_required,
            commands::setup_account,
            commands::login,
            commands::get_current_user,
            commands::update_password,
            commands::create_new_project,
            commands::get_project,
            commands::get_projects,
            commands::update_project_details,
            commands::delete_project_by_id,
            commands::search_projects_by_query,
            commands::create_new_page,
            commands::get_page,
            commands::get_project_pages,
            commands::get_todos,
        ])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
