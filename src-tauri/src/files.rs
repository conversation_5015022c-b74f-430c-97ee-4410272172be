use rusqlite::params;
use serde::{Deserialize, Serialize};
use std::fs;
use std::path::{Path, PathBuf};
use tauri::{App<PERSON><PERSON><PERSON>, Manager};
use uuid::Uuid;

use crate::database::{get_connection, FileAttachment};

#[derive(Debug, Serialize, Deserialize)]
pub struct CreateFileRequest {
    pub project_id: i64,
    pub page_id: Option<i64>,
    pub original_name: String,
    pub file_data: Vec<u8>,
    pub mime_type: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct UpdateFileRequest {
    pub page_id: Option<i64>,
    pub original_name: Option<String>,
}

pub fn get_files_dir(app_handle: &AppHandle) -> Result<PathBuf, Box<dyn std::error::Error>> {
    let app_dir = app_handle
        .path()
        .app_data_dir()
        .map_err(|e| format!("Failed to get app data dir: {}", e))?;
    
    let files_dir = app_dir.join("files");
    std::fs::create_dir_all(&files_dir)
        .map_err(|e| format!("Failed to create files dir: {}", e))?;
    
    Ok(files_dir)
}

pub fn create_file(
    app_handle: &AppHandle,
    request: CreateFileRequest,
) -> Result<FileAttachment, Box<dyn std::error::Error>> {
    let conn = get_connection(app_handle)?;
    let files_dir = get_files_dir(app_handle)?;
    
    // Generate unique filename
    let file_extension = Path::new(&request.original_name)
        .extension()
        .and_then(|ext| ext.to_str())
        .unwrap_or("");
    
    let unique_filename = if file_extension.is_empty() {
        format!("{}", Uuid::new_v4())
    } else {
        format!("{}.{}", Uuid::new_v4(), file_extension)
    };
    
    let file_path = files_dir.join(&unique_filename);
    
    // Write file to disk
    fs::write(&file_path, &request.file_data)?;
    
    // Insert file record into database
    conn.execute(
        "INSERT INTO files (project_id, page_id, filename, original_name, file_path, file_size, mime_type) 
         VALUES (?1, ?2, ?3, ?4, ?5, ?6, ?7)",
        params![
            request.project_id,
            request.page_id,
            unique_filename,
            request.original_name,
            file_path.to_string_lossy().to_string(),
            request.file_data.len() as i64,
            request.mime_type
        ],
    )?;
    
    let file_id = conn.last_insert_rowid();
    get_file_by_id(app_handle, file_id)
}

pub fn get_file_by_id(
    app_handle: &AppHandle,
    file_id: i64,
) -> Result<FileAttachment, Box<dyn std::error::Error>> {
    let conn = get_connection(app_handle)?;
    
    let mut stmt = conn.prepare(
        "SELECT id, project_id, page_id, filename, original_name, file_path, file_size, mime_type, uploaded_at 
         FROM files WHERE id = ?1"
    )?;
    
    let file = stmt.query_row(params![file_id], |row| {
        Ok(FileAttachment {
            id: row.get(0)?,
            project_id: row.get(1)?,
            page_id: row.get(2)?,
            filename: row.get(3)?,
            original_name: row.get(4)?,
            file_path: row.get(5)?,
            file_size: row.get(6)?,
            mime_type: row.get(7)?,
            uploaded_at: row.get(8)?,
        })
    })?;
    
    Ok(file)
}

pub fn get_files_by_project(
    app_handle: &AppHandle,
    project_id: i64,
) -> Result<Vec<FileAttachment>, Box<dyn std::error::Error>> {
    let conn = get_connection(app_handle)?;
    
    let mut stmt = conn.prepare(
        "SELECT id, project_id, page_id, filename, original_name, file_path, file_size, mime_type, uploaded_at 
         FROM files WHERE project_id = ?1 ORDER BY uploaded_at DESC"
    )?;
    
    let file_iter = stmt.query_map(params![project_id], |row| {
        Ok(FileAttachment {
            id: row.get(0)?,
            project_id: row.get(1)?,
            page_id: row.get(2)?,
            filename: row.get(3)?,
            original_name: row.get(4)?,
            file_path: row.get(5)?,
            file_size: row.get(6)?,
            mime_type: row.get(7)?,
            uploaded_at: row.get(8)?,
        })
    })?;
    
    let mut files = Vec::new();
    for file in file_iter {
        files.push(file?);
    }
    
    Ok(files)
}

pub fn get_files_by_page(
    app_handle: &AppHandle,
    page_id: i64,
) -> Result<Vec<FileAttachment>, Box<dyn std::error::Error>> {
    let conn = get_connection(app_handle)?;
    
    let mut stmt = conn.prepare(
        "SELECT id, project_id, page_id, filename, original_name, file_path, file_size, mime_type, uploaded_at 
         FROM files WHERE page_id = ?1 ORDER BY uploaded_at DESC"
    )?;
    
    let file_iter = stmt.query_map(params![page_id], |row| {
        Ok(FileAttachment {
            id: row.get(0)?,
            project_id: row.get(1)?,
            page_id: row.get(2)?,
            filename: row.get(3)?,
            original_name: row.get(4)?,
            file_path: row.get(5)?,
            file_size: row.get(6)?,
            mime_type: row.get(7)?,
            uploaded_at: row.get(8)?,
        })
    })?;
    
    let mut files = Vec::new();
    for file in file_iter {
        files.push(file?);
    }
    
    Ok(files)
}

pub fn get_all_files(
    app_handle: &AppHandle,
) -> Result<Vec<FileAttachment>, Box<dyn std::error::Error>> {
    let conn = get_connection(app_handle)?;
    
    let mut stmt = conn.prepare(
        "SELECT id, project_id, page_id, filename, original_name, file_path, file_size, mime_type, uploaded_at 
         FROM files ORDER BY uploaded_at DESC"
    )?;
    
    let file_iter = stmt.query_map([], |row| {
        Ok(FileAttachment {
            id: row.get(0)?,
            project_id: row.get(1)?,
            page_id: row.get(2)?,
            filename: row.get(3)?,
            original_name: row.get(4)?,
            file_path: row.get(5)?,
            file_size: row.get(6)?,
            mime_type: row.get(7)?,
            uploaded_at: row.get(8)?,
        })
    })?;
    
    let mut files = Vec::new();
    for file in file_iter {
        files.push(file?);
    }
    
    Ok(files)
}

pub fn update_file(
    app_handle: &AppHandle,
    file_id: i64,
    request: UpdateFileRequest,
) -> Result<FileAttachment, Box<dyn std::error::Error>> {
    let conn = get_connection(app_handle)?;
    
    // Build dynamic update query
    let mut updates = Vec::new();
    let mut params_vec: Vec<&dyn rusqlite::ToSql> = Vec::new();
    
    if let Some(page_id) = &request.page_id {
        updates.push("page_id = ?");
        params_vec.push(page_id);
    }
    
    if let Some(original_name) = &request.original_name {
        updates.push("original_name = ?");
        params_vec.push(original_name);
    }
    
    if updates.is_empty() {
        return get_file_by_id(app_handle, file_id);
    }
    
    params_vec.push(&file_id);
    
    let query = format!(
        "UPDATE files SET {} WHERE id = ?",
        updates.join(", ")
    );
    
    conn.execute(&query, &params_vec[..])?;
    
    get_file_by_id(app_handle, file_id)
}

pub fn delete_file(
    app_handle: &AppHandle,
    file_id: i64,
) -> Result<(), Box<dyn std::error::Error>> {
    let conn = get_connection(app_handle)?;
    
    // Get file info first to delete the physical file
    let file = get_file_by_id(app_handle, file_id)?;
    
    // Delete physical file
    if Path::new(&file.file_path).exists() {
        fs::remove_file(&file.file_path)?;
    }
    
    // Delete database record
    conn.execute(
        "DELETE FROM files WHERE id = ?1",
        params![file_id],
    )?;
    
    Ok(())
}

pub fn get_file_data(
    app_handle: &AppHandle,
    file_id: i64,
) -> Result<Vec<u8>, Box<dyn std::error::Error>> {
    let file = get_file_by_id(app_handle, file_id)?;
    let data = fs::read(&file.file_path)?;
    Ok(data)
}

pub fn search_files(
    app_handle: &AppHandle,
    query: &str,
) -> Result<Vec<FileAttachment>, Box<dyn std::error::Error>> {
    let conn = get_connection(app_handle)?;
    
    let search_pattern = format!("%{}%", query);
    
    let mut stmt = conn.prepare(
        "SELECT id, project_id, page_id, filename, original_name, file_path, file_size, mime_type, uploaded_at 
         FROM files 
         WHERE original_name LIKE ?1 OR filename LIKE ?1
         ORDER BY uploaded_at DESC"
    )?;
    
    let file_iter = stmt.query_map(params![search_pattern], |row| {
        Ok(FileAttachment {
            id: row.get(0)?,
            project_id: row.get(1)?,
            page_id: row.get(2)?,
            filename: row.get(3)?,
            original_name: row.get(4)?,
            file_path: row.get(5)?,
            file_size: row.get(6)?,
            mime_type: row.get(7)?,
            uploaded_at: row.get(8)?,
        })
    })?;
    
    let mut files = Vec::new();
    for file in file_iter {
        files.push(file?);
    }
    
    Ok(files)
}
