use rusqlite::params;
use serde::{Deserialize, Serialize};
use serde_json;
use tauri::AppHandle;

use crate::database::get_connection;

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct TableColumn {
    pub id: String,
    pub name: String,
    pub column_type: String, // 'text', 'number', 'date', 'checkbox', 'select'
    pub options: Option<Vec<String>>, // for select type
    pub required: Option<bool>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct CustomTable {
    pub id: i64,
    pub page_id: i64,
    pub name: String,
    pub schema: Vec<TableColumn>,
    pub created_at: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct TableRow {
    pub id: i64,
    pub table_id: i64,
    pub data: serde_json::Value,
    pub position: i64,
    pub created_at: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct CreateTableRequest {
    pub page_id: i64,
    pub name: String,
    pub schema: Vec<TableColumn>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct UpdateTableRequest {
    pub name: Option<String>,
    pub schema: Option<Vec<TableColumn>>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct CreateRowRequest {
    pub table_id: i64,
    pub data: serde_json::Value,
    pub position: Option<i64>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct UpdateRowRequest {
    pub data: Option<serde_json::Value>,
    pub position: Option<i64>,
}

pub fn create_table(
    app_handle: &AppHandle,
    request: CreateTableRequest,
) -> Result<CustomTable, Box<dyn std::error::Error>> {
    let conn = get_connection(app_handle)?;
    
    let schema_json = serde_json::to_string(&request.schema)?;
    
    conn.execute(
        "INSERT INTO custom_tables (page_id, name, schema) VALUES (?1, ?2, ?3)",
        params![request.page_id, request.name, schema_json],
    )?;
    
    let table_id = conn.last_insert_rowid();
    get_table_by_id(app_handle, table_id)
}

pub fn get_table_by_id(
    app_handle: &AppHandle,
    table_id: i64,
) -> Result<CustomTable, Box<dyn std::error::Error>> {
    let conn = get_connection(app_handle)?;
    
    let mut stmt = conn.prepare(
        "SELECT id, page_id, name, schema, created_at FROM custom_tables WHERE id = ?1"
    )?;
    
    let table = stmt.query_row(params![table_id], |row| {
        let schema_json: String = row.get(3)?;
        let schema: Vec<TableColumn> = serde_json::from_str(&schema_json)
            .map_err(|_| rusqlite::Error::InvalidColumnType(3, "schema".to_string(), rusqlite::types::Type::Text))?;
        
        Ok(CustomTable {
            id: row.get(0)?,
            page_id: row.get(1)?,
            name: row.get(2)?,
            schema,
            created_at: row.get(4)?,
        })
    })?;
    
    Ok(table)
}

pub fn get_tables_by_page(
    app_handle: &AppHandle,
    page_id: i64,
) -> Result<Vec<CustomTable>, Box<dyn std::error::Error>> {
    let conn = get_connection(app_handle)?;
    
    let mut stmt = conn.prepare(
        "SELECT id, page_id, name, schema, created_at FROM custom_tables WHERE page_id = ?1 ORDER BY created_at ASC"
    )?;
    
    let table_iter = stmt.query_map(params![page_id], |row| {
        let schema_json: String = row.get(3)?;
        let schema: Vec<TableColumn> = serde_json::from_str(&schema_json)
            .map_err(|_| rusqlite::Error::InvalidColumnType(3, "schema".to_string(), rusqlite::types::Type::Text))?;
        
        Ok(CustomTable {
            id: row.get(0)?,
            page_id: row.get(1)?,
            name: row.get(2)?,
            schema,
            created_at: row.get(4)?,
        })
    })?;
    
    let mut tables = Vec::new();
    for table in table_iter {
        tables.push(table?);
    }
    
    Ok(tables)
}

pub fn update_table(
    app_handle: &AppHandle,
    table_id: i64,
    request: UpdateTableRequest,
) -> Result<CustomTable, Box<dyn std::error::Error>> {
    let conn = get_connection(app_handle)?;
    
    // Build dynamic update query
    let mut updates = Vec::new();
    let mut params_vec: Vec<Box<dyn rusqlite::ToSql>> = Vec::new();
    
    if let Some(name) = &request.name {
        updates.push("name = ?");
        params_vec.push(Box::new(name.clone()));
    }
    
    if let Some(schema) = &request.schema {
        updates.push("schema = ?");
        let schema_json = serde_json::to_string(schema)?;
        params_vec.push(Box::new(schema_json));
    }
    
    if updates.is_empty() {
        return get_table_by_id(app_handle, table_id);
    }
    
    params_vec.push(Box::new(table_id));
    
    let query = format!(
        "UPDATE custom_tables SET {} WHERE id = ?",
        updates.join(", ")
    );
    
    let params_refs: Vec<&dyn rusqlite::ToSql> = params_vec.iter().map(|p| p.as_ref()).collect();
    conn.execute(&query, &params_refs[..])?;
    
    get_table_by_id(app_handle, table_id)
}

pub fn delete_table(
    app_handle: &AppHandle,
    table_id: i64,
) -> Result<(), Box<dyn std::error::Error>> {
    let conn = get_connection(app_handle)?;
    
    // Delete all rows first
    conn.execute(
        "DELETE FROM table_rows WHERE table_id = ?1",
        params![table_id],
    )?;
    
    // Delete the table
    conn.execute(
        "DELETE FROM custom_tables WHERE id = ?1",
        params![table_id],
    )?;
    
    Ok(())
}

pub fn create_row(
    app_handle: &AppHandle,
    request: CreateRowRequest,
) -> Result<TableRow, Box<dyn std::error::Error>> {
    let conn = get_connection(app_handle)?;
    
    let data_json = serde_json::to_string(&request.data)?;
    let position = request.position.unwrap_or(0);
    
    conn.execute(
        "INSERT INTO table_rows (table_id, data, position) VALUES (?1, ?2, ?3)",
        params![request.table_id, data_json, position],
    )?;
    
    let row_id = conn.last_insert_rowid();
    get_row_by_id(app_handle, row_id)
}

pub fn get_row_by_id(
    app_handle: &AppHandle,
    row_id: i64,
) -> Result<TableRow, Box<dyn std::error::Error>> {
    let conn = get_connection(app_handle)?;
    
    let mut stmt = conn.prepare(
        "SELECT id, table_id, data, position, created_at FROM table_rows WHERE id = ?1"
    )?;
    
    let row = stmt.query_row(params![row_id], |row| {
        let data_json: String = row.get(2)?;
        let data: serde_json::Value = serde_json::from_str(&data_json)
            .map_err(|_| rusqlite::Error::InvalidColumnType(2, "data".to_string(), rusqlite::types::Type::Text))?;
        
        Ok(TableRow {
            id: row.get(0)?,
            table_id: row.get(1)?,
            data,
            position: row.get(3)?,
            created_at: row.get(4)?,
        })
    })?;
    
    Ok(row)
}

pub fn get_rows_by_table(
    app_handle: &AppHandle,
    table_id: i64,
) -> Result<Vec<TableRow>, Box<dyn std::error::Error>> {
    let conn = get_connection(app_handle)?;
    
    let mut stmt = conn.prepare(
        "SELECT id, table_id, data, position, created_at FROM table_rows WHERE table_id = ?1 ORDER BY position ASC, created_at ASC"
    )?;
    
    let row_iter = stmt.query_map(params![table_id], |row| {
        let data_json: String = row.get(2)?;
        let data: serde_json::Value = serde_json::from_str(&data_json)
            .map_err(|_| rusqlite::Error::InvalidColumnType(2, "data".to_string(), rusqlite::types::Type::Text))?;
        
        Ok(TableRow {
            id: row.get(0)?,
            table_id: row.get(1)?,
            data,
            position: row.get(3)?,
            created_at: row.get(4)?,
        })
    })?;
    
    let mut rows = Vec::new();
    for row in row_iter {
        rows.push(row?);
    }
    
    Ok(rows)
}

pub fn update_row(
    app_handle: &AppHandle,
    row_id: i64,
    request: UpdateRowRequest,
) -> Result<TableRow, Box<dyn std::error::Error>> {
    let conn = get_connection(app_handle)?;
    
    // Build dynamic update query
    let mut updates = Vec::new();
    let mut params_vec: Vec<Box<dyn rusqlite::ToSql>> = Vec::new();
    
    if let Some(data) = &request.data {
        updates.push("data = ?");
        let data_json = serde_json::to_string(data)?;
        params_vec.push(Box::new(data_json));
    }
    
    if let Some(position) = &request.position {
        updates.push("position = ?");
        params_vec.push(Box::new(*position));
    }
    
    if updates.is_empty() {
        return get_row_by_id(app_handle, row_id);
    }
    
    params_vec.push(Box::new(row_id));
    
    let query = format!(
        "UPDATE table_rows SET {} WHERE id = ?",
        updates.join(", ")
    );
    
    let params_refs: Vec<&dyn rusqlite::ToSql> = params_vec.iter().map(|p| p.as_ref()).collect();
    conn.execute(&query, &params_refs[..])?;
    
    get_row_by_id(app_handle, row_id)
}

pub fn delete_row(
    app_handle: &AppHandle,
    row_id: i64,
) -> Result<(), Box<dyn std::error::Error>> {
    let conn = get_connection(app_handle)?;
    
    conn.execute(
        "DELETE FROM table_rows WHERE id = ?1",
        params![row_id],
    )?;
    
    Ok(())
}
