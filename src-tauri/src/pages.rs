use rusqlite::params;
use serde::{Deserialize, Serialize};
use tauri::AppHandle;

use crate::database::{get_connection, Page};

#[derive(Debug, Serialize, Deserialize)]
pub struct CreatePageRequest {
    pub project_id: i64,
    pub parent_page_id: Option<i64>,
    pub title: String,
    pub content: Option<String>, // JSON string
    pub icon: Option<String>,
    pub position: Option<i64>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct UpdatePageRequest {
    pub title: Option<String>,
    pub content: Option<String>, // JSON string
    pub icon: Option<String>,
    pub position: Option<i64>,
}

pub fn create_page(
    app_handle: &AppHandle,
    request: CreatePageRequest,
) -> Result<Page, Box<dyn std::error::Error>> {
    let conn = get_connection(app_handle)?;
    
    let icon = request.icon.unwrap_or_else(|| "📄".to_string());
    let content = request.content.unwrap_or_else(|| "[]".to_string());
    let position = request.position.unwrap_or(0);
    
    conn.execute(
        "INSERT INTO pages (project_id, parent_page_id, title, content, icon, position) 
         VALUES (?1, ?2, ?3, ?4, ?5, ?6)",
        params![request.project_id, request.parent_page_id, request.title, content, icon, position],
    )?;
    
    let page_id = conn.last_insert_rowid();
    get_page_by_id(app_handle, page_id)
}

pub fn get_page_by_id(
    app_handle: &AppHandle,
    page_id: i64,
) -> Result<Page, Box<dyn std::error::Error>> {
    let conn = get_connection(app_handle)?;
    
    let mut stmt = conn.prepare(
        "SELECT id, project_id, parent_page_id, title, content, icon, position, created_at, updated_at 
         FROM pages WHERE id = ?1"
    )?;
    
    let page = stmt.query_row(params![page_id], |row| {
        Ok(Page {
            id: row.get(0)?,
            project_id: row.get(1)?,
            parent_page_id: row.get(2)?,
            title: row.get(3)?,
            content: row.get(4)?,
            icon: row.get(5)?,
            position: row.get(6)?,
            created_at: row.get(7)?,
            updated_at: row.get(8)?,
        })
    })?;
    
    Ok(page)
}

pub fn get_pages_by_project(
    app_handle: &AppHandle,
    project_id: i64,
) -> Result<Vec<Page>, Box<dyn std::error::Error>> {
    let conn = get_connection(app_handle)?;
    
    let mut stmt = conn.prepare(
        "SELECT id, project_id, parent_page_id, title, content, icon, position, created_at, updated_at 
         FROM pages WHERE project_id = ?1 ORDER BY position ASC, created_at ASC"
    )?;
    
    let page_iter = stmt.query_map(params![project_id], |row| {
        Ok(Page {
            id: row.get(0)?,
            project_id: row.get(1)?,
            parent_page_id: row.get(2)?,
            title: row.get(3)?,
            content: row.get(4)?,
            icon: row.get(5)?,
            position: row.get(6)?,
            created_at: row.get(7)?,
            updated_at: row.get(8)?,
        })
    })?;
    
    let mut pages = Vec::new();
    for page in page_iter {
        pages.push(page?);
    }
    
    Ok(pages)
}

pub fn get_child_pages(
    app_handle: &AppHandle,
    parent_page_id: i64,
) -> Result<Vec<Page>, Box<dyn std::error::Error>> {
    let conn = get_connection(app_handle)?;
    
    let mut stmt = conn.prepare(
        "SELECT id, project_id, parent_page_id, title, content, icon, position, created_at, updated_at 
         FROM pages WHERE parent_page_id = ?1 ORDER BY position ASC, created_at ASC"
    )?;
    
    let page_iter = stmt.query_map(params![parent_page_id], |row| {
        Ok(Page {
            id: row.get(0)?,
            project_id: row.get(1)?,
            parent_page_id: row.get(2)?,
            title: row.get(3)?,
            content: row.get(4)?,
            icon: row.get(5)?,
            position: row.get(6)?,
            created_at: row.get(7)?,
            updated_at: row.get(8)?,
        })
    })?;
    
    let mut pages = Vec::new();
    for page in page_iter {
        pages.push(page?);
    }
    
    Ok(pages)
}

pub fn update_page(
    app_handle: &AppHandle,
    page_id: i64,
    request: UpdatePageRequest,
) -> Result<Page, Box<dyn std::error::Error>> {
    let conn = get_connection(app_handle)?;
    
    // Build dynamic update query
    let mut updates = Vec::new();
    let mut params_vec: Vec<&dyn rusqlite::ToSql> = Vec::new();
    
    if let Some(title) = &request.title {
        updates.push("title = ?");
        params_vec.push(title);
    }
    
    if let Some(content) = &request.content {
        updates.push("content = ?");
        params_vec.push(content);
    }
    
    if let Some(icon) = &request.icon {
        updates.push("icon = ?");
        params_vec.push(icon);
    }
    
    if let Some(position) = &request.position {
        updates.push("position = ?");
        params_vec.push(position);
    }
    
    if updates.is_empty() {
        return get_page_by_id(app_handle, page_id);
    }
    
    updates.push("updated_at = CURRENT_TIMESTAMP");
    params_vec.push(&page_id);
    
    let query = format!(
        "UPDATE pages SET {} WHERE id = ?",
        updates.join(", ")
    );
    
    conn.execute(&query, &params_vec[..])?;
    
    get_page_by_id(app_handle, page_id)
}

pub fn delete_page(
    app_handle: &AppHandle,
    page_id: i64,
) -> Result<(), Box<dyn std::error::Error>> {
    let conn = get_connection(app_handle)?;
    
    // First, recursively delete child pages
    let child_pages = get_child_pages(app_handle, page_id)?;
    for child in child_pages {
        delete_page(app_handle, child.id)?;
    }
    
    // Delete todos associated with this page
    conn.execute(
        "DELETE FROM todos WHERE page_id = ?1",
        params![page_id],
    )?;
    
    // Delete custom tables associated with this page
    conn.execute(
        "DELETE FROM table_rows WHERE table_id IN (
            SELECT id FROM custom_tables WHERE page_id = ?1
        )",
        params![page_id],
    )?;
    
    conn.execute(
        "DELETE FROM custom_tables WHERE page_id = ?1",
        params![page_id],
    )?;
    
    // Delete files associated with this page
    conn.execute(
        "DELETE FROM files WHERE page_id = ?1",
        params![page_id],
    )?;
    
    // Finally delete the page
    conn.execute(
        "DELETE FROM pages WHERE id = ?1",
        params![page_id],
    )?;
    
    Ok(())
}

pub fn search_pages(
    app_handle: &AppHandle,
    query: &str,
    project_id: Option<i64>,
) -> Result<Vec<Page>, Box<dyn std::error::Error>> {
    let conn = get_connection(app_handle)?;
    
    let search_pattern = format!("%{}%", query);
    
    if let Some(proj_id) = project_id {
        let mut stmt = conn.prepare(
            "SELECT id, project_id, parent_page_id, title, content, icon, position, created_at, updated_at
             FROM pages
             WHERE (title LIKE ?1 OR content LIKE ?1) AND project_id = ?2
             ORDER BY updated_at DESC"
        )?;

        let page_iter = stmt.query_map(params![search_pattern, proj_id], |row| {
            Ok(Page {
                id: row.get(0)?,
                project_id: row.get(1)?,
                parent_page_id: row.get(2)?,
                title: row.get(3)?,
                content: row.get(4)?,
                icon: row.get(5)?,
                position: row.get(6)?,
                created_at: row.get(7)?,
                updated_at: row.get(8)?,
            })
        })?;

        let mut pages = Vec::new();
        for page in page_iter {
            pages.push(page?);
        }

        return Ok(pages);
    }

    let mut stmt = conn.prepare(
        "SELECT id, project_id, parent_page_id, title, content, icon, position, created_at, updated_at
         FROM pages
         WHERE title LIKE ?1 OR content LIKE ?1
         ORDER BY updated_at DESC"
    )?;

    let page_iter = stmt.query_map(params![search_pattern], |row| {
        Ok(Page {
            id: row.get(0)?,
            project_id: row.get(1)?,
            parent_page_id: row.get(2)?,
            title: row.get(3)?,
            content: row.get(4)?,
            icon: row.get(5)?,
            position: row.get(6)?,
            created_at: row.get(7)?,
            updated_at: row.get(8)?,
        })
    })?;

    let mut pages = Vec::new();
    for page in page_iter {
        pages.push(page?);
    }

    Ok(pages)
}
