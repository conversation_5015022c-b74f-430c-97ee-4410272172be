use rusqlite::params;
use serde::{Deserialize, Serialize};
use tauri::AppHandle;

use crate::database::{get_connection, Todo};

#[derive(Debug, Serialize, Deserialize)]
pub struct CreateTodoRequest {
    pub page_id: i64,
    pub title: String,
    pub description: Option<String>,
    pub priority: Option<String>, // 'low', 'medium', 'high'
    pub due_date: Option<String>,
    pub position: Option<i64>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct UpdateTodoRequest {
    pub title: Option<String>,
    pub description: Option<String>,
    pub completed: Option<bool>,
    pub priority: Option<String>,
    pub due_date: Option<String>,
    pub position: Option<i64>,
}

pub fn create_todo(
    app_handle: &AppHandle,
    request: CreateTodoRequest,
) -> Result<Todo, Box<dyn std::error::Error>> {
    let conn = get_connection(app_handle)?;
    
    let priority = request.priority.unwrap_or_else(|| "medium".to_string());
    let position = request.position.unwrap_or(0);
    
    conn.execute(
        "INSERT INTO todos (page_id, title, description, priority, due_date, position) 
         VALUES (?1, ?2, ?3, ?4, ?5, ?6)",
        params![request.page_id, request.title, request.description, priority, request.due_date, position],
    )?;
    
    let todo_id = conn.last_insert_rowid();
    get_todo_by_id(app_handle, todo_id)
}

pub fn get_todo_by_id(
    app_handle: &AppHandle,
    todo_id: i64,
) -> Result<Todo, Box<dyn std::error::Error>> {
    let conn = get_connection(app_handle)?;
    
    let mut stmt = conn.prepare(
        "SELECT id, page_id, title, description, completed, priority, due_date, position, created_at, updated_at 
         FROM todos WHERE id = ?1"
    )?;
    
    let todo = stmt.query_row(params![todo_id], |row| {
        Ok(Todo {
            id: row.get(0)?,
            page_id: row.get(1)?,
            title: row.get(2)?,
            description: row.get(3)?,
            completed: row.get(4)?,
            priority: row.get(5)?,
            due_date: row.get(6)?,
            position: row.get(7)?,
            created_at: row.get(8)?,
            updated_at: row.get(9)?,
        })
    })?;
    
    Ok(todo)
}

pub fn get_todos_by_page(
    app_handle: &AppHandle,
    page_id: i64,
) -> Result<Vec<Todo>, Box<dyn std::error::Error>> {
    let conn = get_connection(app_handle)?;
    
    let mut stmt = conn.prepare(
        "SELECT id, page_id, title, description, completed, priority, due_date, position, created_at, updated_at 
         FROM todos WHERE page_id = ?1 ORDER BY position ASC, created_at ASC"
    )?;
    
    let todo_iter = stmt.query_map(params![page_id], |row| {
        Ok(Todo {
            id: row.get(0)?,
            page_id: row.get(1)?,
            title: row.get(2)?,
            description: row.get(3)?,
            completed: row.get(4)?,
            priority: row.get(5)?,
            due_date: row.get(6)?,
            position: row.get(7)?,
            created_at: row.get(8)?,
            updated_at: row.get(9)?,
        })
    })?;
    
    let mut todos = Vec::new();
    for todo in todo_iter {
        todos.push(todo?);
    }
    
    Ok(todos)
}

pub fn get_all_todos(
    app_handle: &AppHandle,
) -> Result<Vec<Todo>, Box<dyn std::error::Error>> {
    let conn = get_connection(app_handle)?;
    
    let mut stmt = conn.prepare(
        "SELECT id, page_id, title, description, completed, priority, due_date, position, created_at, updated_at 
         FROM todos ORDER BY created_at DESC"
    )?;
    
    let todo_iter = stmt.query_map([], |row| {
        Ok(Todo {
            id: row.get(0)?,
            page_id: row.get(1)?,
            title: row.get(2)?,
            description: row.get(3)?,
            completed: row.get(4)?,
            priority: row.get(5)?,
            due_date: row.get(6)?,
            position: row.get(7)?,
            created_at: row.get(8)?,
            updated_at: row.get(9)?,
        })
    })?;
    
    let mut todos = Vec::new();
    for todo in todo_iter {
        todos.push(todo?);
    }
    
    Ok(todos)
}

pub fn update_todo(
    app_handle: &AppHandle,
    todo_id: i64,
    request: UpdateTodoRequest,
) -> Result<Todo, Box<dyn std::error::Error>> {
    let conn = get_connection(app_handle)?;
    
    // Build dynamic update query
    let mut updates = Vec::new();
    let mut params_vec: Vec<&dyn rusqlite::ToSql> = Vec::new();
    
    if let Some(title) = &request.title {
        updates.push("title = ?");
        params_vec.push(title);
    }
    
    if let Some(description) = &request.description {
        updates.push("description = ?");
        params_vec.push(description);
    }
    
    if let Some(completed) = &request.completed {
        updates.push("completed = ?");
        params_vec.push(completed);
    }
    
    if let Some(priority) = &request.priority {
        updates.push("priority = ?");
        params_vec.push(priority);
    }
    
    if let Some(due_date) = &request.due_date {
        updates.push("due_date = ?");
        params_vec.push(due_date);
    }
    
    if let Some(position) = &request.position {
        updates.push("position = ?");
        params_vec.push(position);
    }
    
    if updates.is_empty() {
        return get_todo_by_id(app_handle, todo_id);
    }
    
    updates.push("updated_at = CURRENT_TIMESTAMP");
    params_vec.push(&todo_id);
    
    let query = format!(
        "UPDATE todos SET {} WHERE id = ?",
        updates.join(", ")
    );
    
    conn.execute(&query, &params_vec[..])?;
    
    get_todo_by_id(app_handle, todo_id)
}

pub fn delete_todo(
    app_handle: &AppHandle,
    todo_id: i64,
) -> Result<(), Box<dyn std::error::Error>> {
    let conn = get_connection(app_handle)?;
    
    conn.execute(
        "DELETE FROM todos WHERE id = ?1",
        params![todo_id],
    )?;
    
    Ok(())
}

pub fn search_todos(
    app_handle: &AppHandle,
    query: &str,
) -> Result<Vec<Todo>, Box<dyn std::error::Error>> {
    let conn = get_connection(app_handle)?;
    
    let search_pattern = format!("%{}%", query);
    
    let mut stmt = conn.prepare(
        "SELECT id, page_id, title, description, completed, priority, due_date, position, created_at, updated_at 
         FROM todos 
         WHERE title LIKE ?1 OR description LIKE ?1
         ORDER BY created_at DESC"
    )?;
    
    let todo_iter = stmt.query_map(params![search_pattern], |row| {
        Ok(Todo {
            id: row.get(0)?,
            page_id: row.get(1)?,
            title: row.get(2)?,
            description: row.get(3)?,
            completed: row.get(4)?,
            priority: row.get(5)?,
            due_date: row.get(6)?,
            position: row.get(7)?,
            created_at: row.get(8)?,
            updated_at: row.get(9)?,
        })
    })?;
    
    let mut todos = Vec::new();
    for todo in todo_iter {
        todos.push(todo?);
    }
    
    Ok(todos)
}
