use bcrypt::{hash, verify, DEFAULT_COST};
use rusqlite::params;
use serde::{Deserialize, Serialize};
use tauri::AppHandle;

use crate::database::{get_connection, User};

#[derive(Debug, Serialize, Deserialize)]
pub struct LoginRequest {
    pub username: String,
    pub password: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct SetupRequest {
    pub username: String,
    pub password: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct AuthResponse {
    pub success: bool,
    pub user: Option<UserResponse>,
    pub error: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct UserResponse {
    pub id: i64,
    pub username: String,
    pub created_at: String,
}

impl From<User> for UserResponse {
    fn from(user: User) -> Self {
        UserResponse {
            id: user.id,
            username: user.username,
            created_at: user.created_at,
        }
    }
}

pub fn hash_password(password: &str) -> Result<String, bcrypt::BcryptError> {
    hash(password, DEFAULT_COST)
}

pub fn verify_password(password: &str, hash: &str) -> Result<bool, bcrypt::BcryptError> {
    verify(password, hash)
}

pub fn has_users(app_handle: &AppHandle) -> Result<bool, Box<dyn std::error::Error>> {
    let conn = get_connection(app_handle)?;
    
    let mut stmt = conn.prepare("SELECT COUNT(*) FROM users")?;
    let count: i64 = stmt.query_row([], |row| row.get(0))?;
    
    Ok(count > 0)
}

pub fn create_user(
    app_handle: &AppHandle,
    username: &str,
    password: &str,
) -> Result<UserResponse, Box<dyn std::error::Error>> {
    let conn = get_connection(app_handle)?;
    
    // Check if username already exists
    let mut stmt = conn.prepare("SELECT COUNT(*) FROM users WHERE username = ?1")?;
    let count: i64 = stmt.query_row(params![username], |row| row.get(0))?;
    
    if count > 0 {
        return Err("Username already exists".into());
    }
    
    // Hash password
    let password_hash = hash_password(password)?;
    
    // Insert user
    conn.execute(
        "INSERT INTO users (username, password_hash) VALUES (?1, ?2)",
        params![username, password_hash],
    )?;
    
    // Get the created user
    let user_id = conn.last_insert_rowid();
    let mut stmt = conn.prepare("SELECT id, username, password_hash, created_at FROM users WHERE id = ?1")?;
    let user = stmt.query_row(params![user_id], |row| {
        Ok(User {
            id: row.get(0)?,
            username: row.get(1)?,
            password_hash: row.get(2)?,
            created_at: row.get(3)?,
        })
    })?;
    
    Ok(user.into())
}

pub fn authenticate_user(
    app_handle: &AppHandle,
    username: &str,
    password: &str,
) -> Result<UserResponse, Box<dyn std::error::Error>> {
    let conn = get_connection(app_handle)?;
    
    // Get user by username
    let mut stmt = conn.prepare("SELECT id, username, password_hash, created_at FROM users WHERE username = ?1")?;
    let user = stmt.query_row(params![username], |row| {
        Ok(User {
            id: row.get(0)?,
            username: row.get(1)?,
            password_hash: row.get(2)?,
            created_at: row.get(3)?,
        })
    }).map_err(|_| "Invalid username or password")?;
    
    // Verify password
    if !verify_password(password, &user.password_hash)? {
        return Err("Invalid username or password".into());
    }
    
    Ok(user.into())
}

pub fn get_user_by_id(
    app_handle: &AppHandle,
    user_id: i64,
) -> Result<UserResponse, Box<dyn std::error::Error>> {
    let conn = get_connection(app_handle)?;
    
    let mut stmt = conn.prepare("SELECT id, username, password_hash, created_at FROM users WHERE id = ?1")?;
    let user = stmt.query_row(params![user_id], |row| {
        Ok(User {
            id: row.get(0)?,
            username: row.get(1)?,
            password_hash: row.get(2)?,
            created_at: row.get(3)?,
        })
    })?;
    
    Ok(user.into())
}

pub fn change_password(
    app_handle: &AppHandle,
    user_id: i64,
    old_password: &str,
    new_password: &str,
) -> Result<(), Box<dyn std::error::Error>> {
    let conn = get_connection(app_handle)?;
    
    // Get current user
    let mut stmt = conn.prepare("SELECT password_hash FROM users WHERE id = ?1")?;
    let current_hash: String = stmt.query_row(params![user_id], |row| row.get(0))?;
    
    // Verify old password
    if !verify_password(old_password, &current_hash)? {
        return Err("Current password is incorrect".into());
    }
    
    // Hash new password
    let new_hash = hash_password(new_password)?;
    
    // Update password
    conn.execute(
        "UPDATE users SET password_hash = ?1 WHERE id = ?2",
        params![new_hash, user_id],
    )?;
    
    Ok(())
}
