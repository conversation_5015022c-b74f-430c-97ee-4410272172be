use tauri::{AppHandle, command};
use serde::{Deserialize, Serialize};

use crate::auth::{
    LoginRequest, SetupRequest, UserResponse,
    has_users, create_user, authenticate_user, get_user_by_id, change_password
};
use crate::projects::{
    CreateProjectRequest, UpdateProjectRequest,
    create_project, get_project_by_id, get_all_projects, update_project, delete_project, search_projects
};
use crate::pages::{
    CreatePageRequest,
    create_page, get_page_by_id, get_pages_by_project
};
use crate::todos::{
    get_all_todos
};
use crate::database::{Project, Page, Todo};

#[derive(Debug, Serialize, Deserialize)]
pub struct ApiResponse<T> {
    pub success: bool,
    pub data: Option<T>,
    pub error: Option<String>,
}

impl<T> ApiResponse<T> {
    pub fn success(data: T) -> Self {
        Self {
            success: true,
            data: Some(data),
            error: None,
        }
    }
    
    pub fn error(message: &str) -> Self {
        Self {
            success: false,
            data: None,
            error: Some(message.to_string()),
        }
    }
}

// Authentication Commands
#[command]
pub async fn check_setup_required(app_handle: AppHandle) -> Result<ApiResponse<bool>, String> {
    match has_users(&app_handle) {
        Ok(has_users) => Ok(ApiResponse::success(!has_users)),
        Err(e) => Ok(ApiResponse::error(&e.to_string())),
    }
}

#[command]
pub async fn setup_account(
    app_handle: AppHandle,
    request: SetupRequest,
) -> Result<ApiResponse<UserResponse>, String> {
    if request.username.trim().is_empty() {
        return Ok(ApiResponse::error("Username cannot be empty"));
    }
    
    if request.password.len() < 6 {
        return Ok(ApiResponse::error("Password must be at least 6 characters"));
    }
    
    match create_user(&app_handle, &request.username, &request.password) {
        Ok(user) => Ok(ApiResponse::success(user)),
        Err(e) => Ok(ApiResponse::error(&e.to_string())),
    }
}

#[command]
pub async fn login(
    app_handle: AppHandle,
    request: LoginRequest,
) -> Result<ApiResponse<UserResponse>, String> {
    match authenticate_user(&app_handle, &request.username, &request.password) {
        Ok(user) => Ok(ApiResponse::success(user)),
        Err(e) => Ok(ApiResponse::error(&e.to_string())),
    }
}

#[command]
pub async fn get_current_user(
    app_handle: AppHandle,
    user_id: i64,
) -> Result<ApiResponse<UserResponse>, String> {
    match get_user_by_id(&app_handle, user_id) {
        Ok(user) => Ok(ApiResponse::success(user)),
        Err(e) => Ok(ApiResponse::error(&e.to_string())),
    }
}

#[command]
pub async fn update_password(
    app_handle: AppHandle,
    user_id: i64,
    old_password: String,
    new_password: String,
) -> Result<ApiResponse<()>, String> {
    if new_password.len() < 6 {
        return Ok(ApiResponse::error("Password must be at least 6 characters"));
    }
    
    match change_password(&app_handle, user_id, &old_password, &new_password) {
        Ok(_) => Ok(ApiResponse::success(())),
        Err(e) => Ok(ApiResponse::error(&e.to_string())),
    }
}

// Project Commands
#[command]
pub async fn create_new_project(
    app_handle: AppHandle,
    request: CreateProjectRequest,
) -> Result<ApiResponse<Project>, String> {
    if request.name.trim().is_empty() {
        return Ok(ApiResponse::error("Project name cannot be empty"));
    }
    
    match create_project(&app_handle, request) {
        Ok(project) => Ok(ApiResponse::success(project)),
        Err(e) => Ok(ApiResponse::error(&e.to_string())),
    }
}

#[command]
pub async fn get_project(
    app_handle: AppHandle,
    project_id: i64,
) -> Result<ApiResponse<Project>, String> {
    match get_project_by_id(&app_handle, project_id) {
        Ok(project) => Ok(ApiResponse::success(project)),
        Err(e) => Ok(ApiResponse::error(&e.to_string())),
    }
}

#[command]
pub async fn get_projects(app_handle: AppHandle) -> Result<ApiResponse<Vec<Project>>, String> {
    match get_all_projects(&app_handle) {
        Ok(projects) => Ok(ApiResponse::success(projects)),
        Err(e) => Ok(ApiResponse::error(&e.to_string())),
    }
}

#[command]
pub async fn update_project_details(
    app_handle: AppHandle,
    project_id: i64,
    request: UpdateProjectRequest,
) -> Result<ApiResponse<Project>, String> {
    match update_project(&app_handle, project_id, request) {
        Ok(project) => Ok(ApiResponse::success(project)),
        Err(e) => Ok(ApiResponse::error(&e.to_string())),
    }
}

#[command]
pub async fn delete_project_by_id(
    app_handle: AppHandle,
    project_id: i64,
) -> Result<ApiResponse<()>, String> {
    match delete_project(&app_handle, project_id) {
        Ok(_) => Ok(ApiResponse::success(())),
        Err(e) => Ok(ApiResponse::error(&e.to_string())),
    }
}

#[command]
pub async fn search_projects_by_query(
    app_handle: AppHandle,
    query: String,
) -> Result<ApiResponse<Vec<Project>>, String> {
    if query.trim().is_empty() {
        return get_projects(app_handle).await;
    }

    match search_projects(&app_handle, &query) {
        Ok(projects) => Ok(ApiResponse::success(projects)),
        Err(e) => Ok(ApiResponse::error(&e.to_string())),
    }
}

// Page Commands
#[command]
pub async fn create_new_page(
    app_handle: AppHandle,
    request: CreatePageRequest,
) -> Result<ApiResponse<Page>, String> {
    if request.title.trim().is_empty() {
        return Ok(ApiResponse::error("Page title cannot be empty"));
    }

    match create_page(&app_handle, request) {
        Ok(page) => Ok(ApiResponse::success(page)),
        Err(e) => Ok(ApiResponse::error(&e.to_string())),
    }
}

#[command]
pub async fn get_page(
    app_handle: AppHandle,
    page_id: i64,
) -> Result<ApiResponse<Page>, String> {
    match get_page_by_id(&app_handle, page_id) {
        Ok(page) => Ok(ApiResponse::success(page)),
        Err(e) => Ok(ApiResponse::error(&e.to_string())),
    }
}

#[command]
pub async fn get_project_pages(
    app_handle: AppHandle,
    project_id: i64,
) -> Result<ApiResponse<Vec<Page>>, String> {
    match get_pages_by_project(&app_handle, project_id) {
        Ok(pages) => Ok(ApiResponse::success(pages)),
        Err(e) => Ok(ApiResponse::error(&e.to_string())),
    }
}

// Todo Commands
#[command]
pub async fn get_todos(app_handle: AppHandle) -> Result<ApiResponse<Vec<Todo>>, String> {
    match get_all_todos(&app_handle) {
        Ok(todos) => Ok(ApiResponse::success(todos)),
        Err(e) => Ok(ApiResponse::error(&e.to_string())),
    }
}

#[command]
pub fn test_connection() -> Result<ApiResponse<String>, String> {
    Ok(ApiResponse::success("Connection successful!".to_string()))
}
