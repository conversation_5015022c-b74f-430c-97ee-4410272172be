use tauri::{<PERSON><PERSON><PERSON><PERSON><PERSON>, command};
use serde::{Deserialize, Serialize};

use crate::auth::{
    LoginRequest, SetupRequest, UserResponse,
    has_users, create_user, authenticate_user, get_user_by_id, change_password
};
use crate::projects::{
    CreateProjectRequest, UpdateProjectRequest,
    create_project, get_project_by_id, get_all_projects, update_project, delete_project, search_projects
};
use crate::pages::{
    CreatePageRequest, UpdatePageRequest,
    create_page, get_page_by_id, get_pages_by_project, update_page, delete_page, search_pages
};
use crate::todos::{
    CreateTodoRequest, UpdateTodoRequest,
    create_todo, get_todo_by_id, get_todos_by_page, get_all_todos, update_todo, delete_todo, search_todos
};
use crate::files::{
    CreateFileRequest, UpdateFileRequest,
    create_file, get_file_by_id, get_files_by_project, get_files_by_page, get_all_files,
    update_file, delete_file, get_file_data, search_files
};
use crate::tables::{
    CreateTableRequest, UpdateTableRequest, CreateRowRequest, UpdateRowRequest,
    CustomTable, TableRow,
    create_table, get_table_by_id, get_tables_by_page, update_table, delete_table,
    create_row, get_row_by_id, get_rows_by_table, update_row, delete_row
};
use crate::database::{Project, Page, Todo, FileAttachment};

#[derive(Debug, Serialize, Deserialize)]
pub struct ApiResponse<T> {
    pub success: bool,
    pub data: Option<T>,
    pub error: Option<String>,
}

impl<T> ApiResponse<T> {
    pub fn success(data: T) -> Self {
        Self {
            success: true,
            data: Some(data),
            error: None,
        }
    }
    
    pub fn error(message: &str) -> Self {
        Self {
            success: false,
            data: None,
            error: Some(message.to_string()),
        }
    }
}

// Authentication Commands
#[command]
pub async fn check_setup_required(app_handle: AppHandle) -> Result<ApiResponse<bool>, String> {
    match has_users(&app_handle) {
        Ok(has_users) => Ok(ApiResponse::success(!has_users)),
        Err(e) => Ok(ApiResponse::error(&e.to_string())),
    }
}

#[command]
pub async fn setup_account(
    app_handle: AppHandle,
    request: SetupRequest,
) -> Result<ApiResponse<UserResponse>, String> {
    if request.username.trim().is_empty() {
        return Ok(ApiResponse::error("Username cannot be empty"));
    }
    
    if request.password.len() < 6 {
        return Ok(ApiResponse::error("Password must be at least 6 characters"));
    }
    
    match create_user(&app_handle, &request.username, &request.password) {
        Ok(user) => Ok(ApiResponse::success(user)),
        Err(e) => Ok(ApiResponse::error(&e.to_string())),
    }
}

#[command]
pub async fn login(
    app_handle: AppHandle,
    request: LoginRequest,
) -> Result<ApiResponse<UserResponse>, String> {
    match authenticate_user(&app_handle, &request.username, &request.password) {
        Ok(user) => Ok(ApiResponse::success(user)),
        Err(e) => Ok(ApiResponse::error(&e.to_string())),
    }
}

#[command]
pub async fn get_current_user(
    app_handle: AppHandle,
    user_id: i64,
) -> Result<ApiResponse<UserResponse>, String> {
    match get_user_by_id(&app_handle, user_id) {
        Ok(user) => Ok(ApiResponse::success(user)),
        Err(e) => Ok(ApiResponse::error(&e.to_string())),
    }
}

#[command]
pub async fn update_password(
    app_handle: AppHandle,
    user_id: i64,
    old_password: String,
    new_password: String,
) -> Result<ApiResponse<()>, String> {
    if new_password.len() < 6 {
        return Ok(ApiResponse::error("Password must be at least 6 characters"));
    }
    
    match change_password(&app_handle, user_id, &old_password, &new_password) {
        Ok(_) => Ok(ApiResponse::success(())),
        Err(e) => Ok(ApiResponse::error(&e.to_string())),
    }
}

// Project Commands
#[command]
pub async fn create_new_project(
    app_handle: AppHandle,
    request: CreateProjectRequest,
) -> Result<ApiResponse<Project>, String> {
    if request.name.trim().is_empty() {
        return Ok(ApiResponse::error("Project name cannot be empty"));
    }
    
    match create_project(&app_handle, request) {
        Ok(project) => Ok(ApiResponse::success(project)),
        Err(e) => Ok(ApiResponse::error(&e.to_string())),
    }
}

#[command]
pub async fn get_project(
    app_handle: AppHandle,
    project_id: i64,
) -> Result<ApiResponse<Project>, String> {
    match get_project_by_id(&app_handle, project_id) {
        Ok(project) => Ok(ApiResponse::success(project)),
        Err(e) => Ok(ApiResponse::error(&e.to_string())),
    }
}

#[command]
pub async fn get_projects(app_handle: AppHandle) -> Result<ApiResponse<Vec<Project>>, String> {
    match get_all_projects(&app_handle) {
        Ok(projects) => Ok(ApiResponse::success(projects)),
        Err(e) => Ok(ApiResponse::error(&e.to_string())),
    }
}

#[command]
pub async fn update_project_details(
    app_handle: AppHandle,
    project_id: i64,
    request: UpdateProjectRequest,
) -> Result<ApiResponse<Project>, String> {
    match update_project(&app_handle, project_id, request) {
        Ok(project) => Ok(ApiResponse::success(project)),
        Err(e) => Ok(ApiResponse::error(&e.to_string())),
    }
}

#[command]
pub async fn delete_project_by_id(
    app_handle: AppHandle,
    project_id: i64,
) -> Result<ApiResponse<()>, String> {
    match delete_project(&app_handle, project_id) {
        Ok(_) => Ok(ApiResponse::success(())),
        Err(e) => Ok(ApiResponse::error(&e.to_string())),
    }
}

#[command]
pub async fn search_projects_by_query(
    app_handle: AppHandle,
    query: String,
) -> Result<ApiResponse<Vec<Project>>, String> {
    if query.trim().is_empty() {
        return get_projects(app_handle).await;
    }

    match search_projects(&app_handle, &query) {
        Ok(projects) => Ok(ApiResponse::success(projects)),
        Err(e) => Ok(ApiResponse::error(&e.to_string())),
    }
}

// Page Commands
#[command]
pub async fn create_new_page(
    app_handle: AppHandle,
    request: CreatePageRequest,
) -> Result<ApiResponse<Page>, String> {
    if request.title.trim().is_empty() {
        return Ok(ApiResponse::error("Page title cannot be empty"));
    }

    match create_page(&app_handle, request) {
        Ok(page) => Ok(ApiResponse::success(page)),
        Err(e) => Ok(ApiResponse::error(&e.to_string())),
    }
}

#[command]
pub async fn get_page(
    app_handle: AppHandle,
    page_id: i64,
) -> Result<ApiResponse<Page>, String> {
    match get_page_by_id(&app_handle, page_id) {
        Ok(page) => Ok(ApiResponse::success(page)),
        Err(e) => Ok(ApiResponse::error(&e.to_string())),
    }
}

#[command]
pub async fn get_project_pages(
    app_handle: AppHandle,
    project_id: i64,
) -> Result<ApiResponse<Vec<Page>>, String> {
    match get_pages_by_project(&app_handle, project_id) {
        Ok(pages) => Ok(ApiResponse::success(pages)),
        Err(e) => Ok(ApiResponse::error(&e.to_string())),
    }
}

#[command]
pub async fn update_page_details(
    app_handle: AppHandle,
    page_id: i64,
    request: UpdatePageRequest,
) -> Result<ApiResponse<Page>, String> {
    match update_page(&app_handle, page_id, request) {
        Ok(page) => Ok(ApiResponse::success(page)),
        Err(e) => Ok(ApiResponse::error(&e.to_string())),
    }
}

#[command]
pub async fn delete_page_by_id(
    app_handle: AppHandle,
    page_id: i64,
) -> Result<ApiResponse<()>, String> {
    match delete_page(&app_handle, page_id) {
        Ok(_) => Ok(ApiResponse::success(())),
        Err(e) => Ok(ApiResponse::error(&e.to_string())),
    }
}

#[command]
pub async fn search_pages_by_query(
    app_handle: AppHandle,
    query: String,
    project_id: Option<i64>,
) -> Result<ApiResponse<Vec<Page>>, String> {
    if query.trim().is_empty() {
        return Ok(ApiResponse::success(vec![]));
    }

    match search_pages(&app_handle, &query, project_id) {
        Ok(pages) => Ok(ApiResponse::success(pages)),
        Err(e) => Ok(ApiResponse::error(&e.to_string())),
    }
}

// Todo Commands
#[command]
pub async fn create_new_todo(
    app_handle: AppHandle,
    request: CreateTodoRequest,
) -> Result<ApiResponse<Todo>, String> {
    if request.title.trim().is_empty() {
        return Ok(ApiResponse::error("Todo title cannot be empty"));
    }

    match create_todo(&app_handle, request) {
        Ok(todo) => Ok(ApiResponse::success(todo)),
        Err(e) => Ok(ApiResponse::error(&e.to_string())),
    }
}

#[command]
pub async fn get_todo(
    app_handle: AppHandle,
    todo_id: i64,
) -> Result<ApiResponse<Todo>, String> {
    match get_todo_by_id(&app_handle, todo_id) {
        Ok(todo) => Ok(ApiResponse::success(todo)),
        Err(e) => Ok(ApiResponse::error(&e.to_string())),
    }
}

#[command]
pub async fn get_page_todos(
    app_handle: AppHandle,
    page_id: i64,
) -> Result<ApiResponse<Vec<Todo>>, String> {
    match get_todos_by_page(&app_handle, page_id) {
        Ok(todos) => Ok(ApiResponse::success(todos)),
        Err(e) => Ok(ApiResponse::error(&e.to_string())),
    }
}

#[command]
pub async fn get_todos(app_handle: AppHandle) -> Result<ApiResponse<Vec<Todo>>, String> {
    match get_all_todos(&app_handle) {
        Ok(todos) => Ok(ApiResponse::success(todos)),
        Err(e) => Ok(ApiResponse::error(&e.to_string())),
    }
}

#[command]
pub async fn update_todo_details(
    app_handle: AppHandle,
    todo_id: i64,
    request: UpdateTodoRequest,
) -> Result<ApiResponse<Todo>, String> {
    match update_todo(&app_handle, todo_id, request) {
        Ok(todo) => Ok(ApiResponse::success(todo)),
        Err(e) => Ok(ApiResponse::error(&e.to_string())),
    }
}

#[command]
pub async fn delete_todo_by_id(
    app_handle: AppHandle,
    todo_id: i64,
) -> Result<ApiResponse<()>, String> {
    match delete_todo(&app_handle, todo_id) {
        Ok(_) => Ok(ApiResponse::success(())),
        Err(e) => Ok(ApiResponse::error(&e.to_string())),
    }
}

#[command]
pub async fn search_todos_by_query(
    app_handle: AppHandle,
    query: String,
) -> Result<ApiResponse<Vec<Todo>>, String> {
    if query.trim().is_empty() {
        return get_todos(app_handle).await;
    }

    match search_todos(&app_handle, &query) {
        Ok(todos) => Ok(ApiResponse::success(todos)),
        Err(e) => Ok(ApiResponse::error(&e.to_string())),
    }
}

// File Commands
#[command]
pub async fn upload_file(
    app_handle: AppHandle,
    request: CreateFileRequest,
) -> Result<ApiResponse<FileAttachment>, String> {
    if request.original_name.trim().is_empty() {
        return Ok(ApiResponse::error("Filename cannot be empty"));
    }

    match create_file(&app_handle, request) {
        Ok(file) => Ok(ApiResponse::success(file)),
        Err(e) => Ok(ApiResponse::error(&e.to_string())),
    }
}

#[command]
pub async fn get_file(
    app_handle: AppHandle,
    file_id: i64,
) -> Result<ApiResponse<FileAttachment>, String> {
    match get_file_by_id(&app_handle, file_id) {
        Ok(file) => Ok(ApiResponse::success(file)),
        Err(e) => Ok(ApiResponse::error(&e.to_string())),
    }
}

#[command]
pub async fn get_project_files(
    app_handle: AppHandle,
    project_id: i64,
) -> Result<ApiResponse<Vec<FileAttachment>>, String> {
    match get_files_by_project(&app_handle, project_id) {
        Ok(files) => Ok(ApiResponse::success(files)),
        Err(e) => Ok(ApiResponse::error(&e.to_string())),
    }
}

#[command]
pub async fn get_page_files(
    app_handle: AppHandle,
    page_id: i64,
) -> Result<ApiResponse<Vec<FileAttachment>>, String> {
    match get_files_by_page(&app_handle, page_id) {
        Ok(files) => Ok(ApiResponse::success(files)),
        Err(e) => Ok(ApiResponse::error(&e.to_string())),
    }
}

#[command]
pub async fn get_files(app_handle: AppHandle) -> Result<ApiResponse<Vec<FileAttachment>>, String> {
    match get_all_files(&app_handle) {
        Ok(files) => Ok(ApiResponse::success(files)),
        Err(e) => Ok(ApiResponse::error(&e.to_string())),
    }
}

#[command]
pub async fn update_file_details(
    app_handle: AppHandle,
    file_id: i64,
    request: UpdateFileRequest,
) -> Result<ApiResponse<FileAttachment>, String> {
    match update_file(&app_handle, file_id, request) {
        Ok(file) => Ok(ApiResponse::success(file)),
        Err(e) => Ok(ApiResponse::error(&e.to_string())),
    }
}

#[command]
pub async fn delete_file_by_id(
    app_handle: AppHandle,
    file_id: i64,
) -> Result<ApiResponse<()>, String> {
    match delete_file(&app_handle, file_id) {
        Ok(_) => Ok(ApiResponse::success(())),
        Err(e) => Ok(ApiResponse::error(&e.to_string())),
    }
}

#[command]
pub async fn download_file(
    app_handle: AppHandle,
    file_id: i64,
) -> Result<ApiResponse<Vec<u8>>, String> {
    match get_file_data(&app_handle, file_id) {
        Ok(data) => Ok(ApiResponse::success(data)),
        Err(e) => Ok(ApiResponse::error(&e.to_string())),
    }
}

#[command]
pub async fn search_files_by_query(
    app_handle: AppHandle,
    query: String,
) -> Result<ApiResponse<Vec<FileAttachment>>, String> {
    if query.trim().is_empty() {
        return get_files(app_handle).await;
    }

    match search_files(&app_handle, &query) {
        Ok(files) => Ok(ApiResponse::success(files)),
        Err(e) => Ok(ApiResponse::error(&e.to_string())),
    }
}

// Table Commands
#[command]
pub async fn create_new_table(
    app_handle: AppHandle,
    request: CreateTableRequest,
) -> Result<ApiResponse<CustomTable>, String> {
    if request.name.trim().is_empty() {
        return Ok(ApiResponse::error("Table name cannot be empty"));
    }

    match create_table(&app_handle, request) {
        Ok(table) => Ok(ApiResponse::success(table)),
        Err(e) => Ok(ApiResponse::error(&e.to_string())),
    }
}

#[command]
pub async fn get_table(
    app_handle: AppHandle,
    table_id: i64,
) -> Result<ApiResponse<CustomTable>, String> {
    match get_table_by_id(&app_handle, table_id) {
        Ok(table) => Ok(ApiResponse::success(table)),
        Err(e) => Ok(ApiResponse::error(&e.to_string())),
    }
}

#[command]
pub async fn get_page_tables(
    app_handle: AppHandle,
    page_id: i64,
) -> Result<ApiResponse<Vec<CustomTable>>, String> {
    match get_tables_by_page(&app_handle, page_id) {
        Ok(tables) => Ok(ApiResponse::success(tables)),
        Err(e) => Ok(ApiResponse::error(&e.to_string())),
    }
}

#[command]
pub async fn update_table_details(
    app_handle: AppHandle,
    table_id: i64,
    request: UpdateTableRequest,
) -> Result<ApiResponse<CustomTable>, String> {
    match update_table(&app_handle, table_id, request) {
        Ok(table) => Ok(ApiResponse::success(table)),
        Err(e) => Ok(ApiResponse::error(&e.to_string())),
    }
}

#[command]
pub async fn delete_table_by_id(
    app_handle: AppHandle,
    table_id: i64,
) -> Result<ApiResponse<()>, String> {
    match delete_table(&app_handle, table_id) {
        Ok(_) => Ok(ApiResponse::success(())),
        Err(e) => Ok(ApiResponse::error(&e.to_string())),
    }
}

// Table Row Commands
#[command]
pub async fn create_table_row(
    app_handle: AppHandle,
    request: CreateRowRequest,
) -> Result<ApiResponse<TableRow>, String> {
    match create_row(&app_handle, request) {
        Ok(row) => Ok(ApiResponse::success(row)),
        Err(e) => Ok(ApiResponse::error(&e.to_string())),
    }
}

#[command]
pub async fn get_table_row(
    app_handle: AppHandle,
    row_id: i64,
) -> Result<ApiResponse<TableRow>, String> {
    match get_row_by_id(&app_handle, row_id) {
        Ok(row) => Ok(ApiResponse::success(row)),
        Err(e) => Ok(ApiResponse::error(&e.to_string())),
    }
}

#[command]
pub async fn get_table_rows(
    app_handle: AppHandle,
    table_id: i64,
) -> Result<ApiResponse<Vec<TableRow>>, String> {
    match get_rows_by_table(&app_handle, table_id) {
        Ok(rows) => Ok(ApiResponse::success(rows)),
        Err(e) => Ok(ApiResponse::error(&e.to_string())),
    }
}

#[command]
pub async fn update_table_row(
    app_handle: AppHandle,
    row_id: i64,
    request: UpdateRowRequest,
) -> Result<ApiResponse<TableRow>, String> {
    match update_row(&app_handle, row_id, request) {
        Ok(row) => Ok(ApiResponse::success(row)),
        Err(e) => Ok(ApiResponse::error(&e.to_string())),
    }
}

#[command]
pub async fn delete_table_row(
    app_handle: AppHandle,
    row_id: i64,
) -> Result<ApiResponse<()>, String> {
    match delete_row(&app_handle, row_id) {
        Ok(_) => Ok(ApiResponse::success(())),
        Err(e) => Ok(ApiResponse::error(&e.to_string())),
    }
}

#[command]
pub fn test_connection() -> Result<ApiResponse<String>, String> {
    Ok(ApiResponse::success("Connection successful!".to_string()))
}
